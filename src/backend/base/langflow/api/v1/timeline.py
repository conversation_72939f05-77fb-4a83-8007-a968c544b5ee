"""Timeline API endpoints for research and world-building."""

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from uuid import UUID

from langflow.api.utils import CurrentActiveUser
from langflow.schema.research import (
    Timeline, TimelineCreate, TimelineUpdate, TimelineRead,
    TimelineEvent, TimelineEventCreate, TimelineEventUpdate, TimelineEventRead
)
from langflow.services.database.models.research.timeline_service import TimelineManager
from langflow.services.deps import get_firebase_service
from langflow.logging import logger

router = APIRouter(prefix="/timelines", tags=["timelines"])


@router.post("/", response_model=Timeline)
async def create_timeline(
    timeline_data: TimelineCreate,
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_service)
):
    """Create a new timeline."""
    try:
        timeline_manager = TimelineManager(firebase_service)
        timeline = await timeline_manager.create_timeline(
            timeline_data, workspace_id, current_user.id
        )
        return timeline
    except Exception as e:
        logger.error(f"Error creating timeline: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{timeline_id}", response_model=Timeline)
async def get_timeline(
    timeline_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_service)
):
    """Get a timeline by ID."""
    try:
        timeline_manager = TimelineManager(firebase_service)
        timeline = await timeline_manager.get_timeline(
            timeline_id, workspace_id, current_user.id
        )
        
        if not timeline:
            raise HTTPException(status_code=404, detail="Timeline not found")
        
        return timeline
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting timeline {timeline_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/book/{book_id}", response_model=List[Timeline])
async def get_book_timelines(
    book_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_service)
):
    """Get all timelines for a book."""
    try:
        timeline_manager = TimelineManager(firebase_service)
        timelines = await timeline_manager.get_book_timelines(
            book_id, workspace_id, current_user.id
        )
        return timelines
    except Exception as e:
        logger.error(f"Error getting timelines for book {book_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{timeline_id}", response_model=Timeline)
async def update_timeline(
    timeline_id: str,
    timeline_data: TimelineUpdate,
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_service)
):
    """Update a timeline."""
    try:
        timeline_manager = TimelineManager(firebase_service)
        timeline = await timeline_manager.update_timeline(
            timeline_id, timeline_data, workspace_id, current_user.id
        )
        
        if not timeline:
            raise HTTPException(status_code=404, detail="Timeline not found")
        
        return timeline
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating timeline {timeline_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{timeline_id}")
async def delete_timeline(
    timeline_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_service)
):
    """Delete a timeline and all its events."""
    try:
        timeline_manager = TimelineManager(firebase_service)
        success = await timeline_manager.delete_timeline(
            timeline_id, workspace_id, current_user.id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Timeline not found")
        
        return {"message": "Timeline deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting timeline {timeline_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Timeline Events
@router.post("/{timeline_id}/events", response_model=TimelineEvent)
async def create_timeline_event(
    timeline_id: str,
    event_data: TimelineEventCreate,
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_service)
):
    """Create a new timeline event."""
    try:
        # Ensure the timeline_id in the event data matches the URL parameter
        event_data.timeline_id = UUID(timeline_id)
        
        timeline_manager = TimelineManager(firebase_service)
        event = await timeline_manager.create_timeline_event(
            event_data, workspace_id, current_user.id
        )
        return event
    except Exception as e:
        logger.error(f"Error creating timeline event: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{timeline_id}/events", response_model=List[TimelineEvent])
async def get_timeline_events(
    timeline_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_service),
    limit: Optional[int] = Query(None, description="Maximum number of events to return"),
):
    """Get all events for a timeline."""
    try:
        timeline_manager = TimelineManager(firebase_service)
        events = await timeline_manager.get_timeline_events(
            timeline_id, workspace_id, current_user.id, limit
        )
        return events
    except Exception as e:
        logger.error(f"Error getting events for timeline {timeline_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/events/{event_id}", response_model=TimelineEvent)
async def get_timeline_event(
    event_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_service)
):
    """Get a timeline event by ID."""
    try:
        timeline_manager = TimelineManager(firebase_service)
        event = await timeline_manager.get_timeline_event(
            event_id, workspace_id, current_user.id
        )
        
        if not event:
            raise HTTPException(status_code=404, detail="Timeline event not found")
        
        return event
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting timeline event {event_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/events/{event_id}", response_model=TimelineEvent)
async def update_timeline_event(
    event_id: str,
    event_data: TimelineEventUpdate,
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_service)
):
    """Update a timeline event."""
    try:
        timeline_manager = TimelineManager(firebase_service)
        event = await timeline_manager.update_timeline_event(
            event_id, event_data, workspace_id, current_user.id
        )
        
        if not event:
            raise HTTPException(status_code=404, detail="Timeline event not found")
        
        return event
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating timeline event {event_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/events/{event_id}")
async def delete_timeline_event(
    event_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_service)
):
    """Delete a timeline event."""
    try:
        timeline_manager = TimelineManager(firebase_service)
        success = await timeline_manager.delete_timeline_event(
            event_id, workspace_id, current_user.id
        )
        
        if not success:
            raise HTTPException(status_code=404, detail="Timeline event not found")
        
        return {"message": "Timeline event deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting timeline event {event_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Timeline Visualization
@router.get("/{timeline_id}/visualization")
async def get_timeline_visualization(
    timeline_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_service),
    visualization_type: str = Query("chronological", description="Type of visualization"),
):
    """Generate timeline visualization data."""
    try:
        timeline_manager = TimelineManager(firebase_service)
        visualization_data = await timeline_manager.generate_timeline_visualization(
            timeline_id, workspace_id, current_user.id, visualization_type
        )
        return visualization_data
    except Exception as e:
        logger.error(f"Error generating timeline visualization: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Timeline Export
@router.post("/{timeline_id}/export")
async def export_timeline(
    timeline_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_service),
    export_format: str = Query("json", description="Export format (json, csv, markdown)"),
):
    """Export timeline data."""
    try:
        if export_format not in ["json", "csv", "markdown"]:
            raise HTTPException(status_code=400, detail="Invalid export format")
        
        timeline_manager = TimelineManager(firebase_service)
        export_data = await timeline_manager.export_timeline(
            timeline_id, workspace_id, current_user.id, export_format
        )
        
        return export_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting timeline: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Parallel Timelines
@router.post("/parallel")
async def create_parallel_timeline_group(
    book_id: str,
    timeline_ids: List[str],
    group_name: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_service),
    description: Optional[str] = None,
):
    """Create a parallel timeline group."""
    try:
        timeline_manager = TimelineManager(firebase_service)
        group_data = await timeline_manager.create_parallel_timeline_group(
            book_id, timeline_ids, workspace_id, current_user.id, group_name, description
        )
        return group_data
    except Exception as e:
        logger.error(f"Error creating parallel timeline group: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/parallel/{group_id}/view")
async def get_parallel_timeline_view(
    group_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    firebase_service=Depends(get_firebase_service),
    sync_method: str = Query("automatic", description="Synchronization method"),
):
    """Get synchronized view of parallel timelines."""
    try:
        timeline_manager = TimelineManager(firebase_service)
        parallel_data = await timeline_manager.get_parallel_timeline_view(
            group_id, workspace_id, current_user.id, sync_method
        )
        return parallel_data
    except Exception as e:
        logger.error(f"Error getting parallel timeline view: {e}")
        raise HTTPException(status_code=500, detail=str(e))