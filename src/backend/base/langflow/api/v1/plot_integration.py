"""Plot Integration API endpoints for advanced plot management."""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from loguru import logger

from langflow.api.utils import CurrentActiveUser
from langflow.services.database.models.scene.model import (
    PlotOutlineCreate, PlotOutlineRead, PlotOutlineUpdate,
    PlotMappingRequest, PlotMappingResponse,
    PlotHoleDetectionResponse
)
from langflow.services.deps import get_firebase_scene_service, get_firebase_plot_integration_service


router = APIRouter(prefix="/plot-integration", tags=["Plot Integration"])


@router.post("/outlines", response_model=PlotOutlineRead)
async def create_plot_outline(
    outline_data: PlotOutlineCreate,
    current_user: CurrentActiveUser,
    plot_service = Depends(get_firebase_plot_integration_service)
):
    """Create a new plot outline with standard story structure."""
    try:
        return await plot_service.create_plot_outline(outline_data, current_user.id)
    except Exception as e:
        logger.error(f"Error creating plot outline: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/outlines/{outline_id}", response_model=PlotOutlineRead)
async def get_plot_outline(
    outline_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    plot_service = Depends(get_firebase_plot_integration_service)
):
    """Get a plot outline by ID."""
    try:
        return await plot_service.get_plot_outline(outline_id, workspace_id, current_user.id)
    except Exception as e:
        logger.error(f"Error getting plot outline: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/books/{book_id}/outline", response_model=Optional[PlotOutlineRead])
async def get_plot_outline_by_book(
    book_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    plot_service = Depends(get_firebase_plot_integration_service)
):
    """Get plot outline for a specific book."""
    try:
        return await plot_service.get_plot_outline_by_book(book_id, workspace_id, current_user.id)
    except Exception as e:
        logger.error(f"Error getting plot outline for book: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.put("/outlines/{outline_id}", response_model=PlotOutlineRead)
async def update_plot_outline(
    outline_id: str,
    outline_data: PlotOutlineUpdate,
    workspace_id: str,
    current_user: CurrentActiveUser,
    plot_service = Depends(get_firebase_plot_integration_service)
):
    """Update a plot outline."""
    try:
        return await plot_service.update_plot_outline(outline_id, outline_data, workspace_id, current_user.id)
    except Exception as e:
        logger.error(f"Error updating plot outline: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post("/scenes/map-plot-points", response_model=PlotMappingResponse)
async def map_scene_to_plot_points(
    request: PlotMappingRequest,
    workspace_id: str,
    current_user: CurrentActiveUser,
    plot_service = Depends(get_firebase_plot_integration_service)
):
    """Map a scene to specific plot points."""
    try:
        return await plot_service.map_scene_to_plot_points(request, workspace_id, current_user.id)
    except Exception as e:
        logger.error(f"Error mapping scene to plot points: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/books/{book_id}/progression")
async def get_plot_progression(
    book_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    plot_service = Depends(get_firebase_plot_integration_service)
):
    """Get detailed plot progression metrics for a book."""
    try:
        return await plot_service.calculate_plot_progression(book_id, workspace_id, current_user.id)
    except Exception as e:
        logger.error(f"Error getting plot progression: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/books/{book_id}/plot-holes", response_model=PlotHoleDetectionResponse)
async def detect_plot_holes(
    book_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    plot_service = Depends(get_firebase_plot_integration_service)
):
    """Detect plot holes and story structure issues."""
    try:
        return await plot_service.detect_advanced_plot_holes(book_id, workspace_id, current_user.id)
    except Exception as e:
        logger.error(f"Error detecting plot holes: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/books/{book_id}/tension-curve")
async def get_tension_curve(
    book_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    plot_service = Depends(get_firebase_plot_integration_service)
):
    """Generate tension curve visualization data for a book."""
    try:
        return await plot_service.generate_tension_curve(book_id, workspace_id, current_user.id)
    except Exception as e:
        logger.error(f"Error generating tension curve: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post("/books/{book_id}/milestones")
async def create_plot_milestone(
    book_id: str,
    milestone_data: dict,
    workspace_id: str,
    current_user: CurrentActiveUser,
    plot_service = Depends(get_firebase_plot_integration_service)
):
    """Create a new plot milestone."""
    try:
        milestone_data["book_id"] = book_id
        return await plot_service.create_plot_milestone(milestone_data, workspace_id, current_user.id)
    except Exception as e:
        logger.error(f"Error creating plot milestone: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/books/{book_id}/milestones")
async def get_plot_milestones(
    book_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    plot_service = Depends(get_firebase_plot_integration_service)
):
    """Get all plot milestones for a book."""
    try:
        return await plot_service.get_plot_milestones(book_id, workspace_id, current_user.id)
    except Exception as e:
        logger.error(f"Error getting plot milestones: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.put("/milestones/{milestone_id}")
async def update_milestone_progress(
    milestone_id: str,
    progress_data: dict,
    workspace_id: str,
    current_user: CurrentActiveUser,
    plot_service = Depends(get_firebase_plot_integration_service)
):
    """Update progress on a plot milestone."""
    try:
        return await plot_service.update_milestone_progress(milestone_id, progress_data, workspace_id, current_user.id)
    except Exception as e:
        logger.error(f"Error updating milestone progress: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )