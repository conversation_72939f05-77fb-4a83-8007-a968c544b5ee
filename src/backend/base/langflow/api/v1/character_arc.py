"""Character arc tracking API endpoints."""

from typing import Any, Dict, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPBearer
from sqlmodel import Session

from langflow.api.utils import CurrentActiveUser
from langflow.services.database.models.character.arc_tracking import (
    CharacterArc,
    CharacterArcCreate,
    CharacterArcMilestone,
    CharacterArcMilestoneCreate,
    CharacterArcMilestoneRead,
    CharacterArcMilestoneUpdate,
    CharacterArcRead,
    CharacterArcTemplate,
    CharacterArcTemplateCreate,
    CharacterArcTemplateRead,
    CharacterArcTemplateUpdate,
    CharacterArcUpdate,
    CharacterGrowthMetricCreate,
    CharacterGrowthMetricRead,
)
from langflow.services.database.models.character.arc_tracking_service import CharacterArcTracker
from langflow.services.deps import get_session

security = HTTPBearer()
router = APIRouter(prefix="/character-arcs", tags=["Character Arc Tracking"])


@router.get("/templates", response_model=List[CharacterArcTemplateRead])
async def get_arc_templates(
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
    workspace_id: Optional[str] = None,
    category: Optional[str] = None,
) -> List[CharacterArcTemplateRead]:
    """Get available character arc templates."""
    try:
        # In real implementation, query database for templates
        tracker = CharacterArcTracker()
        
        if not workspace_id:
            # Return built-in templates
            templates = []
            for template_data in tracker.built_in_templates.values():
                if not category or template_data["category"] == category:
                    template = CharacterArcTemplateRead(
                        id="built-in-" + template_data["name"].lower().replace(" ", "-"),
                        workspace_id=None,
                        created_at="2023-01-01T00:00:00Z",
                        updated_at="2023-01-01T00:00:00Z",
                        **template_data
                    )
                    templates.append(template)
            return templates
        
        # In real implementation, return workspace-specific templates
        return []
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch arc templates: {str(e)}"
        )


@router.post("/templates", response_model=CharacterArcTemplateRead)
async def create_arc_template(
    template_data: CharacterArcTemplateCreate,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> CharacterArcTemplateRead:
    """Create a new character arc template."""
    try:
        # In real implementation, save to database
        # Verify workspace access
        # Create template with generated ID
        
        template = CharacterArcTemplateRead(
            id=str(UUID()),
            **template_data.dict(),
            created_at="2023-01-01T00:00:00Z",
            updated_at="2023-01-01T00:00:00Z"
        )
        
        return template
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create arc template: {str(e)}"
        )


@router.get("/templates/{template_id}", response_model=CharacterArcTemplateRead)
async def get_arc_template(
    template_id: str,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> CharacterArcTemplateRead:
    """Get specific arc template by ID."""
    try:
        # Check if it's a built-in template
        if template_id.startswith("built-in-"):
            tracker = CharacterArcTracker()
            template_key = template_id.replace("built-in-", "").replace("-", "_")
            
            if template_key in tracker.built_in_templates:
                template_data = tracker.built_in_templates[template_key]
                return CharacterArcTemplateRead(
                    id=template_id,
                    workspace_id=None,
                    created_at="2023-01-01T00:00:00Z",
                    updated_at="2023-01-01T00:00:00Z",
                    **template_data
                )
        
        # In real implementation, fetch from database
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Arc template not found"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch arc template: {str(e)}"
        )


@router.post("/", response_model=CharacterArcRead)
async def create_character_arc(
    arc_data: CharacterArcCreate,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> CharacterArcRead:
    """Create a new character arc for a character."""
    try:
        # In real implementation:
        # 1. Verify workspace access
        # 2. Verify character exists
        # 3. Create arc in database
        # 4. Create milestones from template
        
        tracker = CharacterArcTracker()
        arc = await tracker.create_character_arc(
            character_id=arc_data.character_id,
            template_id=arc_data.template_id,
            arc_data=arc_data.dict(),
            workspace_id=arc_data.workspace_id
        )
        
        # Return created arc
        return CharacterArcRead(
            id=str(UUID()),
            **arc_data.dict(),
            created_at="2023-01-01T00:00:00Z",
            updated_at="2023-01-01T00:00:00Z"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create character arc: {str(e)}"
        )


@router.get("/{arc_id}", response_model=CharacterArcRead)
async def get_character_arc(
    arc_id: str,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> CharacterArcRead:
    """Get character arc by ID."""
    try:
        # In real implementation, fetch from database and verify access
        
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Character arc not found"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch character arc: {str(e)}"
        )


@router.put("/{arc_id}", response_model=CharacterArcRead)
async def update_character_arc(
    arc_id: str,
    arc_update: CharacterArcUpdate,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> CharacterArcRead:
    """Update character arc."""
    try:
        # In real implementation:
        # 1. Fetch arc from database
        # 2. Verify access
        # 3. Update fields
        # 4. Save to database
        
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Character arc not found"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update character arc: {str(e)}"
        )


@router.post("/{arc_id}/progress", response_model=Dict[str, Any])
async def update_arc_progress(
    arc_id: str,
    progress_data: Dict[str, Any],
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> Dict[str, Any]:
    """Update character arc progress and milestone completion."""
    try:
        chapter_id = progress_data.get("chapter_id")
        progress = progress_data.get("progress", 0.0)
        context = progress_data.get("context", {})
        
        if not chapter_id or not isinstance(progress, (int, float)):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="chapter_id and progress are required"
            )
        
        tracker = CharacterArcTracker()
        result = await tracker.update_arc_progress(
            character_arc_id=arc_id,
            chapter_id=chapter_id,
            progress=progress,
            context=context
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update arc progress: {str(e)}"
        )


@router.post("/{arc_id}/motivation", response_model=Dict[str, str])
async def track_motivation_change(
    arc_id: str,
    motivation_data: Dict[str, Any],
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> Dict[str, str]:
    """Track character motivation changes."""
    try:
        character_id = motivation_data.get("character_id")
        old_motivation = motivation_data.get("old_motivation")
        new_motivation = motivation_data.get("new_motivation")
        chapter_id = motivation_data.get("chapter_id")
        reason = motivation_data.get("reason", "")
        
        if not all([character_id, old_motivation, new_motivation, chapter_id]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="character_id, old_motivation, new_motivation, and chapter_id are required"
            )
        
        tracker = CharacterArcTracker()
        await tracker.track_motivation_change(
            character_id=character_id,
            character_arc_id=arc_id,
            old_motivation=old_motivation,
            new_motivation=new_motivation,
            chapter_id=chapter_id,
            reason=reason
        )
        
        return {"status": "success", "message": "Motivation change tracked"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to track motivation change: {str(e)}"
        )


@router.post("/{arc_id}/conflict", response_model=Dict[str, str])
async def track_conflict(
    arc_id: str,
    conflict_data: Dict[str, Any],
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> Dict[str, str]:
    """Track character conflicts and resolutions."""
    try:
        conflict_type = conflict_data.get("conflict_type")
        description = conflict_data.get("description")
        chapter_id = conflict_data.get("chapter_id")
        status = conflict_data.get("status", "introduced")
        
        if not all([conflict_type, description, chapter_id]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="conflict_type, description, and chapter_id are required"
            )
        
        tracker = CharacterArcTracker()
        await tracker.track_conflict_resolution(
            character_arc_id=arc_id,
            conflict_type=conflict_type,
            description=description,
            chapter_id=chapter_id,
            status=status
        )
        
        return {"status": "success", "message": f"Conflict {status}"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to track conflict: {str(e)}"
        )


@router.post("/{arc_id}/growth", response_model=Dict[str, Any])
async def measure_character_growth(
    arc_id: str,
    growth_data: Dict[str, Any],
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> Dict[str, Any]:
    """Measure and record character growth metrics."""
    try:
        character_id = growth_data.get("character_id")
        chapter_id = growth_data.get("chapter_id")
        
        if not all([character_id, chapter_id]):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="character_id and chapter_id are required"
            )
        
        tracker = CharacterArcTracker()
        result = await tracker.measure_character_growth(
            character_id=character_id,
            character_arc_id=arc_id,
            chapter_id=chapter_id,
            growth_data=growth_data
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to measure character growth: {str(e)}"
        )


@router.get("/{arc_id}/analytics", response_model=Dict[str, Any])
async def get_arc_analytics(
    arc_id: str,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> Dict[str, Any]:
    """Get comprehensive analytics for character arc."""
    try:
        tracker = CharacterArcTracker()
        analytics = await tracker.generate_arc_analytics(arc_id)
        
        return analytics
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate arc analytics: {str(e)}"
        )


@router.get("/{arc_id}/milestones", response_model=List[CharacterArcMilestoneRead])
async def get_arc_milestones(
    arc_id: str,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> List[CharacterArcMilestoneRead]:
    """Get milestones for character arc."""
    try:
        # In real implementation, fetch milestones from database
        return []
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch arc milestones: {str(e)}"
        )


@router.put("/{arc_id}/milestones/{milestone_id}", response_model=CharacterArcMilestoneRead)
async def update_milestone(
    arc_id: str,
    milestone_id: str,
    milestone_update: CharacterArcMilestoneUpdate,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> CharacterArcMilestoneRead:
    """Update character arc milestone."""
    try:
        # In real implementation:
        # 1. Fetch milestone from database
        # 2. Verify access
        # 3. Update fields
        # 4. Save to database
        
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Milestone not found"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update milestone: {str(e)}"
        )


@router.delete("/{arc_id}")
async def delete_character_arc(
    arc_id: str,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> Dict[str, str]:
    """Delete character arc."""
    try:
        # In real implementation:
        # 1. Fetch arc from database
        # 2. Verify access
        # 3. Delete arc and associated milestones
        
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Character arc not found"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete character arc: {str(e)}"
        )