from typing import List, Optional
from uuid import UUID
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, Query, UploadFile, File, Form
from pydantic import BaseModel
from sqlmodel import Session, select

from langflow.api.utils import CurrentActiveUser
from langflow.services.database.models.design import (
    DesignProject,
    DesignProjectCreate,
    DesignProjectRead,
    DesignProjectUpdate,
    DesignElement,
    DesignElementCreate,
    DesignElementRead,
    DesignElementUpdate,
    DesignTemplate,
    DesignTemplateCreate,
    DesignTemplateRead,
    DesignTemplateUpdate,
    DesignAsset,
    DesignAssetCreate,
    DesignAssetRead,
    DesignAssetUpdate,
    DesignVersion,
    DesignVersionCreate,
    DesignVersionRead,
    DesignVersionUpdate,
    BrandKit,
    BrandKitCreate,
    BrandKitRead,
    BrandKitUpdate,
    BrandAsset,
    BrandAssetCreate,
    BrandAssetRead,
    BrandAssetUpdate,
    BrandTemplate,
    BrandTemplateCreate,
    BrandTemplateRead,
    BrandTemplateUpdate,
    AIDesignGenerationRequest,
    AIDesignGenerationResponse,
    StyleTransferRequest,
    PlatformOptimizationRequest,
    ColorPaletteGenerationRequest,
    ColorPaletteGenerationResponse,
)
from langflow.services.deps import get_session
from langflow.services.stock_assets.stock_asset_service import stock_asset_service, AssetFilters, IconFilters
from langflow.services.stock_assets.license_manager import license_manager, UsageType, LicenseType
from langflow.services.stock_assets.advanced_search_service import advanced_search_service, SearchQuery
from langflow.services.stock_assets.collections_service import CollectionsService
from langflow.services.database.models.license import (
    AssetLicenseRead,
    AssetLicenseCreate,
    AssetUsageRecordRead,
    AssetUsageRecordCreate,
    LicenseViolationRead,
    LicenseComplianceReportRead,
    LicenseAlertRead
)

router = APIRouter(prefix="/design", tags=["design"])


# Design Projects endpoints
@router.get("/projects", response_model=List[DesignProjectRead])
async def get_design_projects(
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
    workspace_id: Optional[UUID] = None,
    skip: int = 0,
    limit: int = 100,
):
    """Get design projects for the current user."""
    query = select(DesignProject).where(DesignProject.user_id == current_user.id)

    if workspace_id:
        query = query.where(DesignProject.workspace_id == workspace_id)

    query = query.offset(skip).limit(limit).order_by(DesignProject.updated_at.desc())
    projects = session.exec(query).all()
    return projects


@router.post("/projects", response_model=DesignProjectRead)
async def create_design_project(
    project: DesignProjectCreate,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
):
    """Create a new design project."""
    db_project = DesignProject(
        **project.model_dump(),
        user_id=current_user.id
    )
    session.add(db_project)
    session.commit()
    session.refresh(db_project)
    return db_project


@router.get("/projects/{project_id}", response_model=DesignProjectRead)
async def get_design_project(
    project_id: UUID,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
):
    """Get a specific design project."""
    project = session.get(DesignProject, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Design project not found")

    if project.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this project")

    return project


@router.put("/projects/{project_id}", response_model=DesignProjectRead)
async def update_design_project(
    project_id: UUID,
    project_update: DesignProjectUpdate,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
):
    """Update a design project."""
    project = session.get(DesignProject, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Design project not found")

    if project.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to update this project")

    update_data = project_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(project, field, value)

    session.add(project)
    session.commit()
    session.refresh(project)
    return project


@router.delete("/projects/{project_id}")
async def delete_design_project(
    project_id: UUID,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
):
    """Delete a design project."""
    project = session.get(DesignProject, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Design project not found")

    if project.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this project")

    session.delete(project)
    session.commit()
    return {"message": "Design project deleted successfully"}


# Design Elements endpoints
@router.get("/projects/{project_id}/elements", response_model=List[DesignElementRead])
async def get_design_elements(
    project_id: UUID,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
):
    """Get all elements for a design project."""
    # Verify project access
    project = session.get(DesignProject, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Design project not found")

    if project.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this project")

    query = select(DesignElement).where(DesignElement.project_id == project_id).order_by(DesignElement.z_index)
    elements = session.exec(query).all()
    return elements


@router.post("/projects/{project_id}/elements", response_model=DesignElementRead)
async def create_design_element(
    project_id: UUID,
    element: DesignElementCreate,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
):
    """Create a new design element."""
    # Verify project access
    project = session.get(DesignProject, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Design project not found")

    if project.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to modify this project")

    db_element = DesignElement(**element.model_dump())
    session.add(db_element)
    session.commit()
    session.refresh(db_element)
    return db_element


@router.put("/elements/{element_id}", response_model=DesignElementRead)
async def update_design_element(
    element_id: UUID,
    element_update: DesignElementUpdate,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
):
    """Update a design element."""
    element = session.get(DesignElement, element_id)
    if not element:
        raise HTTPException(status_code=404, detail="Design element not found")

    # Verify project access
    project = session.get(DesignProject, element.project_id)
    if not project or project.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to modify this element")

    update_data = element_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(element, field, value)

    session.add(element)
    session.commit()
    session.refresh(element)
    return element


@router.delete("/elements/{element_id}")
async def delete_design_element(
    element_id: UUID,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
):
    """Delete a design element."""
    element = session.get(DesignElement, element_id)
    if not element:
        raise HTTPException(status_code=404, detail="Design element not found")

    # Verify project access
    project = session.get(DesignProject, element.project_id)
    if not project or project.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to delete this element")

    session.delete(element)
    session.commit()
    return {"message": "Design element deleted successfully"}


# Design Templates endpoints
@router.get("/templates", response_model=List[DesignTemplateRead])
async def get_design_templates(
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
    category: Optional[str] = None,
    is_public: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100,
):
    """Get design templates."""
    query = select(DesignTemplate)

    # Filter by public templates or user's own templates
    if is_public is True:
        query = query.where(DesignTemplate.is_public == True)
    else:
        query = query.where(
            (DesignTemplate.user_id == current_user.id) |
            (DesignTemplate.is_public == True)
        )

    if category:
        query = query.where(DesignTemplate.category == category)

    query = query.offset(skip).limit(limit).order_by(DesignTemplate.usage_count.desc())
    templates = session.exec(query).all()
    return templates


@router.post("/templates", response_model=DesignTemplateRead)
async def create_design_template(
    template: DesignTemplateCreate,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
):
    """Create a new design template."""
    db_template = DesignTemplate(
        **template.model_dump(),
        user_id=current_user.id
    )
    session.add(db_template)
    session.commit()
    session.refresh(db_template)
    return db_template


@router.get("/templates/{template_id}", response_model=DesignTemplateRead)
async def get_design_template(
    template_id: UUID,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
):
    """Get a specific design template."""
    template = session.get(DesignTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Design template not found")

    # Check if user can access this template
    if not template.is_public and template.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this template")

    return template


# Design Assets endpoints
@router.get("/assets", response_model=List[DesignAssetRead])
async def get_design_assets(
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
    workspace_id: Optional[UUID] = None,
    asset_type: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
):
    """Get design assets for the current user."""
    query = select(DesignAsset).where(DesignAsset.user_id == current_user.id)

    if workspace_id:
        query = query.where(DesignAsset.workspace_id == workspace_id)

    if asset_type:
        query = query.where(DesignAsset.asset_type == asset_type)

    query = query.offset(skip).limit(limit).order_by(DesignAsset.created_at.desc())
    assets = session.exec(query).all()
    return assets


@router.post("/assets", response_model=DesignAssetRead)
async def create_design_asset(
    asset: DesignAssetCreate,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
):
    """Create a new design asset."""
    db_asset = DesignAsset(
        **asset.model_dump(),
        user_id=current_user.id
    )
    session.add(db_asset)
    session.commit()
    session.refresh(db_asset)
    return db_asset


# Design Versions endpoints
@router.get("/projects/{project_id}/versions", response_model=List[DesignVersionRead])
async def get_design_versions(
    project_id: UUID,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
):
    """Get all versions for a design project."""
    # Verify project access
    project = session.get(DesignProject, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Design project not found")

    if project.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to access this project")

    query = select(DesignVersion).where(DesignVersion.project_id == project_id).order_by(DesignVersion.version_number.desc())
    versions = session.exec(query).all()
    return versions


@router.post("/projects/{project_id}/versions", response_model=DesignVersionRead)
async def create_design_version(
    project_id: UUID,
    version: DesignVersionCreate,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
):
    """Create a new design version."""
    # Verify project access
    project = session.get(DesignProject, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="Design project not found")

    if project.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized to modify this project")

    db_version = DesignVersion(
        **version.model_dump(),
        user_id=current_user.id
    )
    session.add(db_version)
    session.commit()
    session.refresh(db_version)
    return db_version


# Brand Kit endpoints
@router.get("/brand-kits", response_model=List[BrandKitRead])
async def get_brand_kits(
    current_user: CurrentActiveUser,
    workspace_id: Optional[UUID] = None,
    skip: int = 0,
    limit: int = 100,
):
    """Get brand kits for the current user and workspace."""
    # Note: This would use Firebase service in actual implementation
    # For now, returning empty list as placeholder
    return []


@router.post("/brand-kits", response_model=BrandKitRead)
async def create_brand_kit(
    brand_kit: BrandKitCreate,
    current_user: CurrentActiveUser,
):
    """Create a new brand kit."""
    # Note: This would use Firebase service in actual implementation
    # For now, returning a mock response
    from datetime import datetime
    from uuid import uuid4
    
    mock_brand_kit = BrandKitRead(
        id=str(uuid4()),
        workspace_id=brand_kit.workspace_id,
        user_id=current_user.id,
        created_by=current_user.id,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        usage_count=0,
        last_used_at=None,
        **brand_kit.model_dump()
    )
    return mock_brand_kit


@router.get("/brand-kits/{brand_kit_id}", response_model=BrandKitRead)
async def get_brand_kit(
    brand_kit_id: UUID,
    current_user: CurrentActiveUser,
):
    """Get a specific brand kit."""
    # Note: This would use Firebase service in actual implementation
    raise HTTPException(status_code=404, detail="Brand kit not found")


@router.put("/brand-kits/{brand_kit_id}", response_model=BrandKitRead)
async def update_brand_kit(
    brand_kit_id: UUID,
    brand_kit_update: BrandKitUpdate,
    current_user: CurrentActiveUser,
):
    """Update a brand kit."""
    # Note: This would use Firebase service in actual implementation
    raise HTTPException(status_code=404, detail="Brand kit not found")


@router.post("/brand-kits/{brand_kit_id}/duplicate")
async def duplicate_brand_kit(
    brand_kit_id: UUID,
    new_name: str,
    current_user: CurrentActiveUser,
):
    """Duplicate a brand kit with a new name."""
    # Note: This would use Firebase service in actual implementation
    from datetime import datetime
    from uuid import uuid4
    
    mock_duplicate = BrandKitRead(
        id=str(uuid4()),
        workspace_id="workspace_1",
        user_id=current_user.id,
        created_by=current_user.id,
        name=new_name,
        description=f"Copy of original brand kit",
        primary_colors=["#000000", "#FFFFFF"],
        secondary_colors=["#808080"],
        accent_colors=[],
        neutral_colors=[],
        primary_font="Arial",
        secondary_font=None,
        heading_font=None,
        body_font=None,
        logo_assets=[],
        pattern_assets=[],
        graphic_assets=[],
        brand_guidelines=None,
        color_guidelines=None,
        typography_guidelines=None,
        is_active=True,
        is_default=False,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        usage_count=0,
        last_used_at=None
    )
    return mock_duplicate

@router.post("/brand-kits/{brand_kit_id}/archive")
async def archive_brand_kit(
    brand_kit_id: UUID,
    current_user: CurrentActiveUser,
):
    """Archive a brand kit."""
    # Note: This would use Firebase service in actual implementation
    return {"message": "Brand kit archived successfully"}

@router.get("/brand-kits/{brand_kit_id}/analytics")
async def get_brand_kit_analytics(
    brand_kit_id: UUID,
    current_user: CurrentActiveUser,
):
    """Get analytics for a brand kit."""
    # Note: This would use Firebase service in actual implementation
    return {
        "brand_kit_id": str(brand_kit_id),
        "usage_count": 15,
        "template_count": 5,
        "asset_count": 8,
        "last_used_at": "2025-06-09T12:00:00Z",
        "created_at": "2025-06-01T10:00:00Z",
        "is_active": True
    }

@router.delete("/brand-kits/{brand_kit_id}")
async def delete_brand_kit(
    brand_kit_id: UUID,
    current_user: CurrentActiveUser,
):
    """Delete a brand kit."""
    # Note: This would use Firebase service in actual implementation
    raise HTTPException(status_code=404, detail="Brand kit not found")


@router.post("/brand-kits/{brand_kit_id}/share")
async def share_brand_kit(
    brand_kit_id: UUID,
    share_data: dict,
    current_user: CurrentActiveUser,
):
    """Share a brand kit with specific users or make it public."""
    # Note: This would use Firebase service in actual implementation
    return {
        "brand_kit_id": str(brand_kit_id),
        "shared_with": share_data.get("users", []),
        "permissions": share_data.get("permissions", "view"),
        "is_public": share_data.get("is_public", False),
        "shared_at": "2025-06-09T12:00:00Z"
    }


@router.get("/brand-kits/{brand_kit_id}/permissions")
async def get_brand_kit_permissions(
    brand_kit_id: UUID,
    current_user: CurrentActiveUser,
):
    """Get sharing permissions for a brand kit."""
    # Note: This would use Firebase service in actual implementation
    return {
        "brand_kit_id": str(brand_kit_id),
        "owner": current_user.id,
        "shared_users": [
            {"user_id": "user_2", "username": "<EMAIL>", "permissions": "edit"},
            {"user_id": "user_3", "username": "<EMAIL>", "permissions": "view"}
        ],
        "is_public": False,
        "created_at": "2025-06-01T10:00:00Z"
    }


@router.put("/brand-kits/{brand_kit_id}/permissions")
async def update_brand_kit_permissions(
    brand_kit_id: UUID,
    permissions_data: dict,
    current_user: CurrentActiveUser,
):
    """Update sharing permissions for a brand kit."""
    # Note: This would use Firebase service in actual implementation
    return {
        "brand_kit_id": str(brand_kit_id),
        "permissions_updated": True,
        "updated_at": "2025-06-09T12:00:00Z"
    }


@router.post("/brand-kits/{brand_kit_id}/check-consistency")
async def check_brand_consistency(
    brand_kit_id: UUID,
    canvas_data: dict,
    current_user: CurrentActiveUser,
):
    """Check brand consistency for a design canvas."""
    # Note: This would use Firebase service in actual implementation
    return {
        "violations": [],
        "suggestions": [],
        "compliance_score": 100.0,
        "total_elements_checked": 0,
        "brand_kit_id": str(brand_kit_id)
    }


@router.post("/brand-kits/{brand_kit_id}/generate-templates")
async def generate_brand_templates(
    brand_kit_id: UUID,
    template_types: List[str],
    current_user: CurrentActiveUser,
):
    """Generate templates from a brand kit."""
    # Note: This would use Firebase service in actual implementation
    return {"message": "Templates generated", "count": len(template_types)}


# Brand Asset endpoints
@router.get("/brand-kits/{brand_kit_id}/assets", response_model=List[BrandAssetRead])
async def get_brand_assets(
    brand_kit_id: UUID,
    current_user: CurrentActiveUser,
    asset_type: Optional[str] = None,
):
    """Get assets for a brand kit."""
    # Note: This would use Firebase service in actual implementation
    return []


@router.post("/brand-kits/{brand_kit_id}/assets", response_model=BrandAssetRead)
async def create_brand_asset(
    brand_kit_id: UUID,
    asset: BrandAssetCreate,
    current_user: CurrentActiveUser,
):
    """Create a new brand asset."""
    # Note: This would use Firebase service in actual implementation
    from datetime import datetime
    from uuid import uuid4
    
    mock_asset = BrandAssetRead(
        id=str(uuid4()),
        brand_kit_id=str(brand_kit_id),
        workspace_id=asset.workspace_id,
        user_id=current_user.id,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        usage_count=0,
        last_used_at=None,
        **asset.model_dump()
    )
    return mock_asset


@router.post("/brand-kits/{brand_kit_id}/assets/upload")
async def upload_brand_asset(
    brand_kit_id: UUID,
    file_data: dict,
    current_user: CurrentActiveUser,
    workspace_id: str,
):
    """Upload and process a brand asset with metadata extraction."""
    # Note: This would use Firebase service in actual implementation
    from datetime import datetime
    from uuid import uuid4
    
    # Mock comprehensive upload processing
    mock_upload_result = {
        "asset_id": str(uuid4()),
        "brand_kit_id": str(brand_kit_id),
        "upload_status": "completed",
        "file_url": f"https://storage.example.com/brand-assets/{uuid4()}.{file_data.get('format', 'png')}",
        "metadata": {
            "width": 1920,
            "height": 1080,
            "file_size": file_data.get('size', 2048000),
            "color_mode": "RGB",
            "dpi": 300
        },
        "auto_tags": ["logo", "branding", "identity"],
        "suggested_categories": ["logo", "primary_assets"],
        "processing_time": 1.2,
        "created_at": datetime.utcnow().isoformat()
    }
    
    return mock_upload_result


@router.get("/brand-kits/{brand_kit_id}/assets/search")
async def search_brand_assets(
    brand_kit_id: UUID,
    current_user: CurrentActiveUser,
    search_text: Optional[str] = None,
    asset_type: Optional[str] = None,
    tags: Optional[List[str]] = None,
    categories: Optional[List[str]] = None,
    sort_by: str = "updated_at",
    sort_order: str = "desc",
    skip: int = 0,
    limit: int = 50,
):
    """Advanced search and filtering for brand assets."""
    # Note: This would use Firebase service in actual implementation
    return {
        "assets": [],
        "total_count": 0,
        "filters_applied": {
            "search_text": search_text,
            "asset_type": asset_type,
            "tags": tags or [],
            "categories": categories or []
        },
        "sort": {"by": sort_by, "order": sort_order},
        "pagination": {"skip": skip, "limit": limit}
    }


@router.put("/brand-kits/{brand_kit_id}/assets/{asset_id}")
async def update_brand_asset_endpoint(
    brand_kit_id: UUID,
    asset_id: UUID,
    asset_update: BrandAssetUpdate,
    current_user: CurrentActiveUser,
):
    """Update brand asset metadata."""
    # Note: This would use Firebase service in actual implementation
    from datetime import datetime
    
    return {
        "asset_id": str(asset_id),
        "brand_kit_id": str(brand_kit_id),
        "updated_fields": list(asset_update.model_dump(exclude_unset=True).keys()),
        "updated_at": datetime.utcnow().isoformat()
    }


@router.delete("/brand-kits/{brand_kit_id}/assets/{asset_id}")
async def delete_brand_asset_endpoint(
    brand_kit_id: UUID,
    asset_id: UUID,
    current_user: CurrentActiveUser,
):
    """Delete a brand asset."""
    # Note: This would use Firebase service in actual implementation
    return {
        "asset_id": str(asset_id),
        "brand_kit_id": str(brand_kit_id),
        "deleted": True,
        "deleted_at": "2025-06-09T12:00:00Z"
    }


@router.post("/brand-kits/{brand_kit_id}/assets/categorize")
async def categorize_brand_assets(
    brand_kit_id: UUID,
    category_updates: dict,
    current_user: CurrentActiveUser,
):
    """Bulk categorize and tag multiple assets."""
    # Note: This would use Firebase service in actual implementation
    return {
        "brand_kit_id": str(brand_kit_id),
        "assets_updated": len(category_updates),
        "updated_at": "2025-06-09T12:00:00Z",
        "status": "completed"
    }


@router.get("/brand-kits/{brand_kit_id}/assets/{asset_id}/versions")
async def get_asset_versions(
    brand_kit_id: UUID,
    asset_id: UUID,
    current_user: CurrentActiveUser,
):
    """Get version history for a brand asset."""
    # Note: This would use Firebase service in actual implementation
    return {
        "asset_id": str(asset_id),
        "versions": [
            {
                "version_id": "v1",
                "version_number": 1,
                "file_url": "https://storage.example.com/v1/asset.png",
                "change_description": "Initial upload",
                "created_at": "2025-06-01T10:00:00Z",
                "is_current": True
            }
        ],
        "total_versions": 1
    }


@router.post("/brand-kits/{brand_kit_id}/assets/{asset_id}/versions")
async def create_asset_version(
    brand_kit_id: UUID,
    asset_id: UUID,
    version_data: dict,
    current_user: CurrentActiveUser,
):
    """Create a new version of an asset."""
    # Note: This would use Firebase service in actual implementation
    from uuid import uuid4
    
    return {
        "version_id": str(uuid4()),
        "asset_id": str(asset_id),
        "version_number": version_data.get('version_number', 2),
        "file_url": version_data.get('file_url'),
        "change_description": version_data.get('change_description', ''),
        "created_at": "2025-06-09T12:00:00Z",
        "is_current": version_data.get('is_current', False)
    }


@router.post("/brand-kits/{brand_kit_id}/assets/{asset_id}/convert")
async def convert_asset_format(
    brand_kit_id: UUID,
    asset_id: UUID,
    target_format: str,
    current_user: CurrentActiveUser,
):
    """Convert asset to a different format."""
    # Note: This would use Firebase service in actual implementation
    return {
        "asset_id": str(asset_id),
        "original_format": "png",
        "target_format": target_format,
        "converted_url": f"https://storage.example.com/converted/{asset_id}.{target_format}",
        "status": "completed",
        "conversion_time": 2.5,
        "quality_score": 0.95
    }


@router.post("/brand-kits/{brand_kit_id}/assets/{asset_id}/optimize")
async def optimize_brand_asset(
    brand_kit_id: UUID,
    asset_id: UUID,
    optimization_params: dict,
    current_user: CurrentActiveUser,
):
    """Optimize asset for web, print, or other uses."""
    # Note: This would use Firebase service in actual implementation
    return {
        "asset_id": str(asset_id),
        "optimization_type": optimization_params.get('type', 'web'),
        "original_size": 2048000,
        "optimized_size": 1433600,  # 30% reduction
        "compression_ratio": 0.3,
        "quality_retained": 0.95,
        "optimized_url": f"https://storage.example.com/optimized/{asset_id}_optimized.png",
        "status": "completed"
    }


@router.get("/brand-kits/{brand_kit_id}/assets/{asset_id}/analytics")
async def get_asset_analytics(
    brand_kit_id: UUID,
    asset_id: UUID,
    current_user: CurrentActiveUser,
):
    """Get detailed usage analytics for an asset."""
    # Note: This would use Firebase service in actual implementation
    return {
        "asset_id": str(asset_id),
        "total_usage_count": 12,
        "template_usage": 7,
        "design_usage": 5,
        "usage_by_month": {
            "2025-06": 5,
            "2025-05": 4,
            "2025-04": 3
        },
        "popular_contexts": [
            {"type": "social_media", "count": 6},
            {"type": "business_cards", "count": 4},
            {"type": "presentations", "count": 2}
        ],
        "performance_score": 0.85
    }


# Brand Template endpoints
@router.get("/brand-kits/{brand_kit_id}/templates", response_model=List[BrandTemplateRead])
async def get_brand_templates(
    brand_kit_id: UUID,
    current_user: CurrentActiveUser,
    template_type: Optional[str] = None,
):
    """Get templates for a brand kit."""
    # Note: This would use Firebase service in actual implementation
    return []


@router.post("/brand-kits/{brand_kit_id}/templates", response_model=BrandTemplateRead)
async def create_brand_template(
    brand_kit_id: UUID,
    template: BrandTemplateCreate,
    current_user: CurrentActiveUser,
):
    """Create a new brand template."""
    # Note: This would use Firebase service in actual implementation
    from datetime import datetime
    from uuid import uuid4
    
    mock_template = BrandTemplateRead(
        id=str(uuid4()),
        brand_kit_id=str(brand_kit_id),
        workspace_id=template.workspace_id,
        user_id=current_user.id,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        usage_count=0,
        last_used_at=None,
        **template.model_dump()
    )
    return mock_template


# Brand Guideline Enforcement endpoints
@router.post("/brand-kits/{brand_kit_id}/enforce-colors")
async def enforce_color_palette(
    brand_kit_id: UUID,
    canvas_data: dict,
    current_user: CurrentActiveUser,
):
    """Enforce brand color palette compliance."""
    # Note: This would use BrandGuidelineEnforcementService in actual implementation
    return {
        "violations": [
            {
                "type": "color_violation",
                "element_color": "#FF0000",
                "violation": "Color #FF0000 is not brand compliant",
                "suggested_color": "#000000",
                "confidence": 85
            }
        ],
        "suggestions": [
            {
                "type": "color_suggestion",
                "action": "replace_color",
                "from_color": "#FF0000",
                "to_color": "#000000",
                "reason": "Better brand compliance"
            }
        ],
        "compliance_score": 85.0,
        "total_colors_checked": 3
    }


@router.post("/brand-kits/{brand_kit_id}/check-fonts")
async def check_font_guidelines(
    brand_kit_id: UUID,
    canvas_data: dict,
    current_user: CurrentActiveUser,
):
    """Check font guideline compliance."""
    # Note: This would use BrandGuidelineEnforcementService in actual implementation
    return {
        "violations": [],
        "suggestions": [],
        "compliance_score": 100.0,
        "total_fonts_checked": 2
    }


@router.post("/brand-kits/{brand_kit_id}/auto-suggestions")
async def generate_automatic_suggestions(
    brand_kit_id: UUID,
    canvas_data: dict,
    current_user: CurrentActiveUser,
):
    """Generate automatic brand guideline suggestions."""
    # Note: This would use BrandGuidelineEnforcementService in actual implementation
    return [
        {
            "type": "color_suggestion",
            "action": "replace_color",
            "from_color": "#FF0000",
            "to_color": "#000000",
            "reason": "Better brand compliance"
        },
        {
            "type": "layout_suggestion",
            "action": "add_logo",
            "reason": "Brand logo should be included in design",
            "assets": ["logo1.svg", "logo2.svg"]
        }
    ]


@router.post("/brand-kits/{brand_kit_id}/validate-style")
async def validate_style_guidelines(
    brand_kit_id: UUID,
    canvas_data: dict,
    current_user: CurrentActiveUser,
):
    """Validate overall style guideline compliance."""
    # Note: This would use BrandGuidelineEnforcementService in actual implementation
    return {
        "violations": [],
        "compliance_score": 92.0,
        "categories": {
            "color_score": 85.0,
            "font_score": 100.0,
            "spacing_score": 95.0,
            "proportion_score": 88.0
        }
    }


@router.post("/brand-kits/{brand_kit_id}/rule-config")
async def create_brand_rule_configuration(
    brand_kit_id: UUID,
    rules: dict,
    current_user: CurrentActiveUser,
):
    """Create custom brand rule configuration."""
    # Note: This would use BrandGuidelineEnforcementService in actual implementation
    return {
        "brand_kit_id": str(brand_kit_id),
        "rules": rules,
        "created_at": "2025-06-09T12:00:00Z",
        "is_active": True
    }


@router.get("/workspaces/{workspace_id}/brand-violations")
async def get_violation_report(
    workspace_id: UUID,
    current_user: CurrentActiveUser,
    brand_kit_id: Optional[UUID] = None,
    time_range: str = "7d",
):
    """Generate comprehensive brand violation report."""
    # Note: This would use BrandGuidelineEnforcementService in actual implementation
    return {
        "workspace_id": str(workspace_id),
        "brand_kit_id": str(brand_kit_id) if brand_kit_id else None,
        "time_range": time_range,
        "total_violations": 2,
        "violations_by_type": {
            "color": 1,
            "font": 1,
            "spacing": 0,
            "proportion": 0
        },
        "violations_by_severity": {
            "high": 1,
            "medium": 1,
            "low": 0
        },
        "compliance_trend": [
            {"date": "2025-06-03", "score": 85},
            {"date": "2025-06-09", "score": 89}
        ]
    }


# AI Design Generation endpoints
@router.post("/ai/generate", response_model=AIDesignGenerationResponse)
async def generate_design_from_ai(
    request: AIDesignGenerationRequest,
    current_user: CurrentActiveUser,
):
    """Generate design using AI from natural language prompt."""
    # Note: This would use AIDesignGenerationService in actual implementation
    import time
    from datetime import datetime
    
    # Mock AI generation response
    mock_canvas_data = {
        "width": 1080,
        "height": 1080,
        "elements": [
            {
                "type": "rect",
                "x": 0,
                "y": 0,
                "width": 1080,
                "height": 1080,
                "fill": "#F8F9FA",
                "id": "background"
            },
            {
                "type": "text",
                "x": 540,
                "y": 540,
                "text": request.prompt[:30] + "...",
                "fontSize": 48,
                "fontFamily": "Arial",
                "fill": "#000000",
                "textAlign": "center",
                "id": "main_text"
            }
        ]
    }
    
    return AIDesignGenerationResponse(
        canvas_data=mock_canvas_data,
        design_description=f"AI-generated {request.style} design based on: {request.prompt}",
        ai_confidence=0.85,
        suggestions=[
            "Consider adjusting the font size for better readability",
            "Try using brand colors if available",
            "Add visual elements to enhance the design"
        ],
        generation_time=1.2,
        tokens_used=150
    )


@router.post("/ai/style-transfer")
async def apply_style_transfer(
    request: StyleTransferRequest,
    current_user: CurrentActiveUser,
):
    """Apply style transfer to existing design."""
    # Note: This would use AIDesignGenerationService in actual implementation
    
    # Mock style transfer - apply basic style changes
    styled_canvas = request.canvas_data.copy()
    
    # Apply style-specific modifications
    for element in styled_canvas.get("elements", []):
        if element.get("type") == "text":
            if request.style == "vintage":
                element["fill"] = "#8B4513"  # Brown for vintage
                element["fontFamily"] = "Georgia"
            elif request.style == "modern":
                element["fill"] = "#2563EB"  # Blue for modern
                element["fontFamily"] = "Helvetica"
            elif request.style == "playful":
                element["fill"] = "#DC2626"  # Red for playful
                element["fontSize"] = element.get("fontSize", 32) + 8
        elif element.get("type") == "rect" and element.get("id") == "background":
            if request.style == "vintage":
                element["fill"] = "#FDF6E3"  # Vintage background
            elif request.style == "modern":
                element["fill"] = "#F1F5F9"  # Modern background
    
    return {
        "canvas_data": styled_canvas,
        "style_applied": request.style,
        "preservation_score": 0.9,
        "style_application_score": 0.85
    }


@router.post("/ai/platform-optimize")
async def optimize_for_platform(
    request: PlatformOptimizationRequest,
    current_user: CurrentActiveUser,
):
    """Optimize design for specific platform."""
    # Note: This would use AIDesignGenerationService in actual implementation
    
    platform_specs = {
        "instagram_post": {"width": 1080, "height": 1080},
        "instagram_story": {"width": 1080, "height": 1920},
        "facebook_cover": {"width": 1200, "height": 630},
        "twitter_header": {"width": 1500, "height": 500},
        "linkedin_post": {"width": 1200, "height": 628}
    }
    
    target_spec = platform_specs.get(request.target_platform, {"width": 1080, "height": 1080})
    
    # Simple resize logic
    original_canvas = request.canvas_data
    optimized_canvas = {
        "width": target_spec["width"],
        "height": target_spec["height"],
        "elements": []
    }
    
    # Scale elements proportionally
    scale_x = target_spec["width"] / original_canvas.get("width", 1080)
    scale_y = target_spec["height"] / original_canvas.get("height", 1080)
    scale = min(scale_x, scale_y)  # Maintain aspect ratio
    
    for element in original_canvas.get("elements", []):
        new_element = element.copy()
        if "x" in new_element:
            new_element["x"] = int(new_element["x"] * scale)
        if "y" in new_element:
            new_element["y"] = int(new_element["y"] * scale)
        if "width" in new_element:
            new_element["width"] = int(new_element["width"] * scale)
        if "height" in new_element:
            new_element["height"] = int(new_element["height"] * scale)
        if "fontSize" in new_element:
            new_element["fontSize"] = int(new_element["fontSize"] * scale)
        
        optimized_canvas["elements"].append(new_element)
    
    return {
        "canvas_data": optimized_canvas,
        "target_platform": request.target_platform,
        "optimization_score": 0.9,
        "scaling_factor": scale
    }


@router.post("/ai/color-palette", response_model=ColorPaletteGenerationResponse)
async def generate_color_palette(
    request: ColorPaletteGenerationRequest,
    current_user: CurrentActiveUser,
):
    """Generate AI-powered color palette."""
    # Note: This would use AIDesignGenerationService in actual implementation
    
    # Mock color palette generation
    mood_palettes = {
        "energetic": ["#FF6B35", "#F7931E", "#FFD23F", "#06FFA5", "#118AB2"],
        "calm": ["#264653", "#2A9D8F", "#E9C46A", "#F4A261", "#E76F51"],
        "professional": ["#003049", "#D62828", "#F77F00", "#FCBF49", "#EAE2B7"],
        "playful": ["#F72585", "#B5179E", "#7209B7", "#480CA8", "#3A0CA3"],
        "elegant": ["#2D3748", "#4A5568", "#718096", "#A0AEC0", "#CBD5E0"]
    }
    
    if request.mood and request.mood.lower() in mood_palettes:
        colors = mood_palettes[request.mood.lower()][:request.color_count]
    else:
        # Default modern palette
        colors = ["#2563EB", "#7C3AED", "#DC2626", "#059669", "#EA580C"][:request.color_count]
    
    # Extend with brand colors if provided
    if request.brand_colors:
        colors = request.brand_colors[:2] + colors[:request.color_count - len(request.brand_colors[:2])]
    
    color_names = [f"Color {i+1}" for i in range(len(colors))]
    
    return ColorPaletteGenerationResponse(
        colors=colors,
        color_names=color_names,
        mood_match=0.88,
        accessibility_score=0.92,
        harmony_type="complementary"
    )


@router.post("/ai/layout-optimize")
async def optimize_layout(
    canvas_data: dict,
    current_user: CurrentActiveUser,
):
    """AI-powered layout optimization."""
    # Note: This would use AIDesignGenerationService in actual implementation
    
    # Mock layout optimization
    optimized_canvas = canvas_data.copy()
    improvements = []
    
    # Check for overlapping elements
    elements = optimized_canvas.get("elements", [])
    for i, element in enumerate(elements):
        if element.get("type") == "text" and element.get("y", 0) < 50:
            # Move text away from top edge
            element["y"] = 80
            improvements.append(f"Moved text element {i+1} away from edge")
    
    return {
        "canvas_data": optimized_canvas,
        "improvements": improvements,
        "layout_score": 0.85,
        "suggestions": [
            "Consider adding more white space around text elements",
            "Align elements to a grid for better visual hierarchy",
            "Use consistent spacing between elements"
        ]
    }


# Brand Consistency Checking Endpoints

@router.post("/brand-kits/{brand_kit_id}/check-consistency")
async def check_brand_consistency(
    brand_kit_id: UUID,
    canvas_data: dict,
    current_user: CurrentActiveUser,
    detailed_analysis: bool = True,
    include_suggestions: bool = True,
):
    """
    Comprehensive brand consistency checking for design canvas.
    
    Performs advanced analysis including:
    - Color palette compliance
    - Typography guidelines adherence  
    - Logo usage compliance
    - Spacing and layout consistency
    - Brand asset usage validation
    - Accessibility compliance
    """
    try:
        # Note: This would use Firebase brand kit service in actual implementation
        from langflow.services.database.providers.firebase_brand_kit_service import FirebaseBrandKitService
        from langflow.services.deps import get_firebase_db
        
        # Mock Firebase service for demonstration
        # In real implementation, would get Firebase client
        firebase_service = None  # FirebaseBrandKitService(get_firebase_db())
        
        if firebase_service:
            result = await firebase_service.check_brand_consistency(
                canvas_data=canvas_data,
                brand_kit_id=str(brand_kit_id),
                user_id=current_user.id,
                detailed_analysis=detailed_analysis,
                include_suggestions=include_suggestions
            )
            return result
        else:
            # Mock response for development
            return {
                "brand_kit_id": str(brand_kit_id),
                "analysis_type": "detailed" if detailed_analysis else "simple",
                "overall_score": 78.5,
                "violations": [
                    {
                        "id": "color_violation_1",
                        "type": "color_compliance",
                        "severity": "medium",
                        "element_id": "text_1",
                        "description": "Color #ff3366 is not in brand palette",
                        "suggestion": "Use brand color #e53e3e instead",
                        "confidence": 0.9,
                        "position": {"x": 100, "y": 200}
                    },
                    {
                        "id": "typography_violation_1", 
                        "type": "typography_compliance",
                        "severity": "high",
                        "element_id": "text_2",
                        "description": "Font 'Comic Sans' is not in brand guidelines",
                        "suggestion": "Use brand font 'Inter' instead",
                        "confidence": 0.95,
                        "position": {"x": 300, "y": 150}
                    }
                ],
                "suggestions": [
                    {
                        "id": "color_optimization_1",
                        "category": "color_optimization",
                        "title": "Simplify Color Palette",
                        "description": "Reduce from 8 to 5 colors for better consistency",
                        "impact_score": 0.8,
                        "complexity": "medium",
                        "estimated_time": "30-45 minutes"
                    }
                ] if include_suggestions else [],
                "scores": {
                    "overall": 78.5,
                    "color_compliance": 72.0,
                    "typography_compliance": 85.0,
                    "spacing_compliance": 80.0,
                    "logo_compliance": 90.0,
                    "asset_compliance": 75.0
                },
                "timestamp": "2025-06-09T10:30:00Z"
            }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Brand consistency check failed: {str(e)}"
        )


@router.post("/brand-kits/{brand_kit_id}/realtime-analysis")
async def start_realtime_analysis(
    brand_kit_id: UUID,
    session_config: dict,
    current_user: CurrentActiveUser,
):
    """
    Start real-time brand consistency analysis session.
    
    Enables live feedback as the user makes design changes.
    """
    try:
        # Note: This would use the real-time feedback service
        from datetime import datetime
        from uuid import uuid4
        
        session_id = str(uuid4())
        
        # Mock session creation
        return {
            "session_id": session_id,
            "brand_kit_id": str(brand_kit_id),
            "user_id": current_user.id,
            "status": "active",
            "settings": {
                "feedback_enabled": session_config.get("feedback_enabled", True),
                "auto_fix_enabled": session_config.get("auto_fix_enabled", False),
                "debounce_delay": session_config.get("debounce_delay", 1.0)
            },
            "websocket_url": f"ws://localhost:8000/ws/brand-consistency/{session_id}",
            "created_at": datetime.utcnow().isoformat(),
            "message": "Real-time analysis session started"
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start real-time analysis: {str(e)}"
        )


@router.get("/brand-kits/{brand_kit_id}/consistency-trends")
async def get_consistency_trends(
    brand_kit_id: UUID,
    current_user: CurrentActiveUser,
    time_period_days: int = 30,
):
    """
    Get brand consistency trends and analytics over time.
    """
    try:
        # Note: This would use Firebase service and reporting system
        return {
            "brand_kit_id": str(brand_kit_id),
            "time_period_days": time_period_days,
            "trend_data": {
                "score_progression": [
                    {"date": "2025-06-01", "score": 75.0},
                    {"date": "2025-06-02", "score": 76.5},
                    {"date": "2025-06-03", "score": 78.0},
                    {"date": "2025-06-04", "score": 79.2},
                    {"date": "2025-06-05", "score": 78.8},
                    {"date": "2025-06-06", "score": 80.1},
                    {"date": "2025-06-07", "score": 81.3},
                    {"date": "2025-06-08", "score": 82.0},
                    {"date": "2025-06-09", "score": 83.5}
                ],
                "violation_trends": {
                    "color": [5, 4, 3, 3, 2, 2, 1, 1, 0],
                    "typography": [3, 3, 2, 2, 2, 1, 1, 1, 1],
                    "spacing": [2, 1, 1, 0, 0, 0, 0, 0, 0]
                },
                "improvement_rate": 12.5,
                "trend_direction": "improving"
            },
            "insights": [
                "Brand consistency improved by 12.5% over the last 30 days",
                "Color violations have been eliminated completely", 
                "Typography consistency remains the main area for improvement"
            ],
            "recommendations": [
                "Continue focus on typography guidelines",
                "Implement automated color checking",
                "Create team training on brand standards"
            ]
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get consistency trends: {str(e)}"
        )


@router.post("/brand-kits/{brand_kit_id}/generate-report")
async def generate_consistency_report(
    brand_kit_id: UUID,
    current_user: CurrentActiveUser,
    report_type: str = "summary",
    time_range: Optional[dict] = None,
):
    """
    Generate comprehensive brand consistency report.
    
    Report types: summary, detailed, audit, trends, team_performance, project_comparison
    """
    try:
        # Note: This would use the reporting service
        from datetime import datetime
        from uuid import uuid4
        
        report_id = str(uuid4())
        
        # Mock report generation
        return {
            "report_id": report_id,
            "brand_kit_id": str(brand_kit_id),
            "report_type": report_type,
            "status": "generating",
            "estimated_completion": "2025-06-09T10:35:00Z",
            "download_url": f"/api/v1/design/reports/{report_id}/download",
            "preview_url": f"/api/v1/design/reports/{report_id}/preview",
            "created_at": datetime.utcnow().isoformat(),
            "message": f"Started generating {report_type} report for brand kit {brand_kit_id}"
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate report: {str(e)}"
        )


@router.get("/brand-consistency/dashboard")
async def get_consistency_dashboard(
    current_user: CurrentActiveUser,
    workspace_id: Optional[UUID] = None,
    time_frame: str = "last_30_days",
):
    """
    Get real-time brand consistency dashboard data.
    """
    try:
        # Note: This would use the reporting service dashboard functionality
        from datetime import datetime
        
        return {
            "overview_metrics": {
                "overall_score": 81.2,
                "score_change": 2.5,
                "score_trend": "up",
                "total_violations": 15,
                "violation_change": -8,
                "projects_analyzed": 12,
                "compliance_rate": 81.2,
                "improvement_opportunities": 3
            },
            "category_breakdown": {
                "categories": [
                    {
                        "category": "Color Usage",
                        "score": 85.0,
                        "status": "good",
                        "change": 3.0,
                        "violations": 2
                    },
                    {
                        "category": "Typography",
                        "score": 78.0,
                        "status": "needs_improvement", 
                        "change": -1.0,
                        "violations": 5
                    },
                    {
                        "category": "Spacing Layout",
                        "score": 82.0,
                        "status": "good",
                        "change": 4.0,
                        "violations": 3
                    },
                    {
                        "category": "Logo Compliance", 
                        "score": 92.0,
                        "status": "excellent",
                        "change": 1.0,
                        "violations": 1
                    }
                ]
            },
            "recent_violations": {
                "violations": [
                    {
                        "timestamp": "2025-06-09T09:45:00Z",
                        "project_id": "project_1",
                        "type": "color",
                        "severity": "medium",
                        "description": "Off-brand color used in header",
                        "element_id": "header_bg"
                    },
                    {
                        "timestamp": "2025-06-09T09:30:00Z",
                        "project_id": "project_2", 
                        "type": "typography",
                        "severity": "high",
                        "description": "Non-brand font in body text",
                        "element_id": "body_text_1"
                    }
                ],
                "total_count": 15
            },
            "suggestions": {
                "suggestions": [
                    {
                        "category": "typography_enhancement",
                        "impact": 0.8,
                        "title": "Improve Typography Consistency",
                        "priority": "high"
                    },
                    {
                        "category": "color_optimization",
                        "impact": 0.6,
                        "title": "Optimize Color Palette Usage",
                        "priority": "medium"
                    }
                ]
            },
            "alerts": {
                "alerts": [
                    {
                        "type": "warning",
                        "title": "Typography Compliance Declining",
                        "message": "Typography violations increased by 25% this week",
                        "action": "Review typography guidelines with team"
                    }
                ]
            },
            "project_rankings": {
                "rankings": [
                    {"project": "project_1", "score": 89.5, "rank": 1},
                    {"project": "project_2", "score": 85.2, "rank": 2},
                    {"project": "project_3", "score": 78.9, "rank": 3}
                ]
            },
            "quick_actions": {
                "actions": [
                    {
                        "title": "Fix Typography Violations",
                        "description": "Apply brand fonts to 5 recent designs",
                        "estimated_time": "15 minutes",
                        "impact": "high"
                    },
                    {
                        "title": "Run Brand Audit",
                        "description": "Check latest designs for compliance",
                        "estimated_time": "5 minutes",
                        "impact": "medium"
                    }
                ]
            },
            "last_updated": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get dashboard data: {str(e)}"
        )


# Stock Photo Search Models
class StockPhotoSearchRequest(BaseModel):
    """Request model for stock photo search."""
    query: str
    limit: int = 20
    orientation: Optional[str] = None  # landscape, portrait, square
    color: Optional[str] = None
    category: Optional[str] = None
    min_width: Optional[int] = None
    min_height: Optional[int] = None


class StockPhotoResponse(BaseModel):
    """Response model for stock photo."""
    id: str
    url: str
    thumb_url: str
    title: str
    author: str
    source: str
    license_type: str
    width: int
    height: int
    tags: List[str]
    download_url: str
    price: Optional[float] = None
    relevance_score: Optional[float] = None
    attribution: Optional[str] = None


class IconSearchRequest(BaseModel):
    """Request model for icon search."""
    query: str
    style: str = "outline"  # outline, filled, duotone, line, solid
    limit: int = 50
    category: Optional[str] = None
    collection: Optional[str] = None
    size: Optional[str] = None


class IconResponse(BaseModel):
    """Response model for icon."""
    id: str
    name: str
    svg_content: str
    categories: List[str]
    tags: List[str]
    style: str
    source: str
    license_type: str
    collection: Optional[str] = None
    is_customizable: bool = True
    color_variants: List[str] = []


class GraphicSearchRequest(BaseModel):
    """Request model for graphics search."""
    query: str
    limit: int = 20
    orientation: Optional[str] = None
    color: Optional[str] = None
    category: Optional[str] = None
    file_format: Optional[str] = None  # svg, png, jpg


class GraphicResponse(BaseModel):
    """Response model for graphic."""
    id: str
    url: str
    thumb_url: str
    title: str
    author: str
    source: str
    license_type: str
    width: int
    height: int
    tags: List[str]
    categories: List[str]
    file_format: str
    download_url: str
    attribution: Optional[str] = None
    is_vector: bool = False


class IconCustomizationRequest(BaseModel):
    """Request model for icon customization."""
    icon_id: str
    color: Optional[str] = None
    size: Optional[int] = None


# Stock Photo Search Endpoints
@router.post("/assets/stock/photos/search", response_model=List[StockPhotoResponse])
async def search_stock_photos(
    search_request: StockPhotoSearchRequest,
    current_user: CurrentActiveUser,
):
    """Search for stock photos across multiple providers."""
    try:
        filters = AssetFilters(
            limit=search_request.limit,
            orientation=search_request.orientation,
            color=search_request.color,
            category=search_request.category,
            min_width=search_request.min_width,
            min_height=search_request.min_height
        )
        
        photos = await stock_asset_service.search_photos(search_request.query, filters)
        
        return [
            StockPhotoResponse(
                id=photo.id,
                url=photo.url,
                thumb_url=photo.thumb_url,
                title=photo.title,
                author=photo.author,
                source=photo.source,
                license_type=photo.license_type,
                width=photo.width,
                height=photo.height,
                tags=photo.tags,
                download_url=photo.download_url,
                price=photo.price,
                relevance_score=photo.relevance_score,
                attribution=photo.attribution
            )
            for photo in photos
        ]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search stock photos: {str(e)}"
        )


@router.post("/assets/stock/icons/search", response_model=List[IconResponse])
async def search_stock_icons(
    search_request: IconSearchRequest,
    current_user: CurrentActiveUser,
):
    """Search for icons from Iconify with advanced filtering."""
    try:
        filters = IconFilters(
            limit=search_request.limit,
            style=search_request.style,
            category=search_request.category,
            collection=search_request.collection,
            size=search_request.size
        )
        
        icons = await stock_asset_service.search_icons(search_request.query, filters)
        
        return [
            IconResponse(
                id=icon.id,
                name=icon.name,
                svg_content=icon.svg_content,
                categories=icon.categories,
                tags=icon.tags,
                style=icon.style,
                source=icon.source,
                license_type=icon.license_type,
                collection=icon.collection,
                is_customizable=icon.is_customizable,
                color_variants=icon.color_variants
            )
            for icon in icons
        ]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search icons: {str(e)}"
        )


@router.post("/assets/stock/photos/{photo_id}/import", response_model=DesignAssetRead)
async def import_stock_photo(
    photo_id: str,
    workspace_id: UUID,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
):
    """Import a stock photo as a design asset."""
    try:
        # In a real implementation, we would:
        # 1. Fetch the photo metadata from the stock service
        # 2. Download and cache the image
        # 3. Extract metadata and create the asset
        # 4. Track usage for licensing
        
        # For now, create a mock response based on the photo_id
        source = photo_id.split("_")[0]
        stock_id = photo_id.replace(f"{source}_", "")
        
        asset_data = DesignAssetCreate(
            name=f"Stock photo from {source}",
            asset_type="image",
            file_url=f"https://example.com/cached/{photo_id}.jpg",
            file_size=1024000,  # 1MB example
            file_format="jpg",
            width=1920,
            height=1080,
            thumbnail_url=f"https://example.com/thumbs/{photo_id}_thumb.jpg",
            asset_metadata={
                "imported_from_stock": True,
                "import_date": datetime.utcnow().isoformat()
            },
            tags=["stock", source],
            is_stock=True,
            stock_source=source,
            stock_id=stock_id,
            license_type="free" if source in ["unsplash", "pexels"] else "premium",
            attribution=f"Stock photo from {source}",
            workspace_id=str(workspace_id)
        )
        
        db_asset = DesignAsset(**asset_data.model_dump(), user_id=current_user.id)
        session.add(db_asset)
        session.commit()
        session.refresh(db_asset)
        
        return db_asset
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to import stock photo: {str(e)}"
        )


@router.get("/assets/stock/providers")
async def get_stock_providers(current_user: CurrentActiveUser):
    """Get available stock asset providers and their status."""
    providers = []
    
    if stock_asset_service.unsplash:
        providers.append({
            "name": "Unsplash",
            "type": "photos",
            "license": "free",
            "status": "active",
            "features": ["search", "high_resolution", "attribution_required"]
        })
    
    if stock_asset_service.pexels:
        providers.append({
            "name": "Pexels", 
            "type": "photos",
            "license": "free",
            "status": "active",
            "features": ["search", "high_resolution", "attribution_required"]
        })
        
    if stock_asset_service.shutterstock:
        providers.append({
            "name": "Shutterstock",
            "type": "photos",
            "license": "premium",
            "status": "active", 
            "features": ["search", "high_resolution", "commercial_license"]
        })
    
    providers.append({
        "name": "Iconify",
        "type": "icons",
        "license": "free",
        "status": "active",
        "features": ["search", "svg_format", "multiple_styles"]
    })
    
    return {
        "providers": providers,
        "total_providers": len(providers)
    }


@router.post("/assets/stock/graphics/search", response_model=List[GraphicResponse])
async def search_stock_graphics(
    search_request: GraphicSearchRequest,
    current_user: CurrentActiveUser,
):
    """Search for graphics and illustrations."""
    try:
        filters = AssetFilters(
            limit=search_request.limit,
            orientation=search_request.orientation,
            color=search_request.color,
            category=search_request.category
        )
        
        graphics = await stock_asset_service.search_graphics(search_request.query, filters)
        
        return [
            GraphicResponse(
                id=graphic.id,
                url=graphic.url,
                thumb_url=graphic.thumb_url,
                title=graphic.title,
                author=graphic.author,
                source=graphic.source,
                license_type=graphic.license_type,
                width=graphic.width,
                height=graphic.height,
                tags=graphic.tags,
                categories=graphic.categories,
                file_format=graphic.file_format,
                download_url=graphic.download_url,
                attribution=graphic.attribution,
                is_vector=graphic.is_vector
            )
            for graphic in graphics
        ]
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search graphics: {str(e)}"
        )


@router.post("/assets/stock/icons/customize")
async def customize_stock_icon(
    customization_request: IconCustomizationRequest,
    current_user: CurrentActiveUser,
):
    """Customize an icon with color and size options."""
    try:
        customized_svg = await stock_asset_service.customize_icon(
            icon_id=customization_request.icon_id,
            color=customization_request.color,
            size=customization_request.size
        )
        
        return {
            "icon_id": customization_request.icon_id,
            "customized_svg": customized_svg,
            "customizations": {
                "color": customization_request.color,
                "size": customization_request.size
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to customize icon: {str(e)}"
        )


@router.get("/assets/stock/icons/collections")
async def get_icon_collections(current_user: CurrentActiveUser):
    """Get available icon collections."""
    try:
        collections = await stock_asset_service.get_icon_collections()
        
        # Transform to a more usable format
        formatted_collections = []
        for collection_id, collection_data in collections.items():
            formatted_collections.append({
                "id": collection_id,
                "name": collection_data.get("name", collection_id),
                "total": collection_data.get("total", 0),
                "category": collection_data.get("category", "general"),
                "author": collection_data.get("author", {}).get("name", ""),
                "license": collection_data.get("license", {}).get("title", "free"),
                "samples": collection_data.get("samples", [])[:8]  # First 8 sample icons
            })
        
        return {
            "collections": formatted_collections,
            "total_collections": len(formatted_collections)
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get icon collections: {str(e)}"
        )


@router.post("/assets/stock/icons/{icon_id}/optimize")
async def optimize_icon_svg(
    icon_id: str,
    svg_content: str,
    current_user: CurrentActiveUser,
):
    """Optimize SVG content for web use."""
    try:
        optimized_svg = await stock_asset_service.optimize_icon_svg(svg_content)
        
        return {
            "icon_id": icon_id,
            "original_size": len(svg_content),
            "optimized_size": len(optimized_svg),
            "compression_ratio": 1 - (len(optimized_svg) / len(svg_content)),
            "optimized_svg": optimized_svg
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to optimize SVG: {str(e)}"
        )


# License Management Endpoints

@router.post("/assets/stock/usage/track")
async def track_stock_asset_usage(
    usage_data: AssetUsageRecordCreate,
    workspace_id: UUID,
    current_user: CurrentActiveUser,
):
    """Track usage of a stock asset for license compliance."""
    try:
        usage_record = await license_manager.track_asset_usage(
            asset_id=usage_data.asset_id,
            user_id=current_user.id,
            workspace_id=str(workspace_id),
            usage_type=UsageType(usage_data.usage_type),
            project_id=usage_data.project_id,
            platform=usage_data.platform,
            circulation_count=usage_data.circulation_count,
            geographic_region=usage_data.geographic_region,
            metadata=usage_data.metadata
        )
        
        return {
            "usage_record_id": usage_record.id,
            "asset_id": usage_record.asset_id,
            "usage_type": usage_record.usage_type.value,
            "timestamp": usage_record.timestamp.isoformat(),
            "compliance_check": "completed",
            "message": "Asset usage tracked successfully"
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to track asset usage: {str(e)}"
        )


@router.get("/workspaces/{workspace_id}/license-compliance")
async def get_workspace_license_compliance(
    workspace_id: UUID,
    current_user: CurrentActiveUser,
):
    """Get comprehensive license compliance report for a workspace."""
    try:
        compliance_report = await license_manager.check_workspace_compliance(str(workspace_id))
        return compliance_report
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get compliance report: {str(e)}"
        )


@router.get("/workspaces/{workspace_id}/license-violations")
async def get_workspace_license_violations(
    workspace_id: UUID,
    current_user: CurrentActiveUser,
    resolved: Optional[bool] = None,
    severity: Optional[str] = None,
    limit: int = 50,
    skip: int = 0,
):
    """Get license violations for a workspace with filtering options."""
    try:
        # Get all violations for the workspace
        workspace_violations = [
            v for v in license_manager.violations 
            if any(r.workspace_id == str(workspace_id) and r.asset_id == v.asset_id 
                  for r in license_manager.usage_records)
        ]
        
        # Apply filters
        if resolved is not None:
            workspace_violations = [v for v in workspace_violations if v.resolved == resolved]
        
        if severity:
            workspace_violations = [v for v in workspace_violations if v.severity.value == severity]
        
        # Apply pagination
        total_count = len(workspace_violations)
        paginated_violations = workspace_violations[skip:skip + limit]
        
        return {
            "violations": [
                {
                    "id": v.id,
                    "asset_id": v.asset_id,
                    "violation_type": v.violation_type,
                    "severity": v.severity.value,
                    "description": v.description,
                    "recommendation": v.recommendation,
                    "detected_at": v.detected_at.isoformat(),
                    "resolved": v.resolved,
                    "resolved_at": v.resolved_at.isoformat() if v.resolved_at else None,
                    "resolution_notes": v.resolution_notes
                }
                for v in paginated_violations
            ],
            "total_count": total_count,
            "skip": skip,
            "limit": limit,
            "has_more": skip + limit < total_count
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get violations: {str(e)}"
        )


@router.post("/license-violations/{violation_id}/resolve")
async def resolve_license_violation(
    violation_id: str,
    resolution_notes: str,
    current_user: CurrentActiveUser,
):
    """Mark a license violation as resolved."""
    try:
        success = await license_manager.resolve_violation(
            violation_id=violation_id,
            user_id=current_user.id,
            resolution_notes=resolution_notes
        )
        
        if success:
            return {
                "violation_id": violation_id,
                "resolved": True,
                "resolved_by": current_user.id,
                "resolved_at": datetime.utcnow().isoformat(),
                "resolution_notes": resolution_notes,
                "message": "Violation resolved successfully"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Violation not found"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to resolve violation: {str(e)}"
        )


@router.post("/assets/stock/license/purchase")
async def purchase_premium_license(
    asset_id: str,
    license_type: str,
    current_user: CurrentActiveUser,
):
    """Purchase a premium license for a stock asset."""
    try:
        # Validate license type
        try:
            license_enum = LicenseType(license_type)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid license type: {license_type}"
            )
        
        payment_result = await license_manager.process_premium_license_payment(
            asset_id=asset_id,
            user_id=current_user.id,
            license_type=license_enum
        )
        
        return payment_result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to purchase license: {str(e)}"
        )


@router.get("/workspaces/{workspace_id}/license-expiration-alerts")
async def get_license_expiration_alerts(
    workspace_id: UUID,
    current_user: CurrentActiveUser,
    days_ahead: int = 30,
):
    """Get alerts for licenses expiring within specified days."""
    try:
        alerts = await license_manager.get_license_expiration_alerts(
            workspace_id=str(workspace_id),
            days_ahead=days_ahead
        )
        
        return {
            "workspace_id": str(workspace_id),
            "alerts": alerts,
            "total_alerts": len(alerts),
            "days_ahead": days_ahead,
            "critical_alerts": len([a for a in alerts if a["severity"] == "critical"]),
            "warning_alerts": len([a for a in alerts if a["severity"] == "warning"])
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get expiration alerts: {str(e)}"
        )


@router.post("/workspaces/{workspace_id}/license-usage-report")
async def generate_license_usage_report(
    workspace_id: UUID,
    start_date: datetime,
    end_date: datetime,
    current_user: CurrentActiveUser,
):
    """Generate comprehensive license usage report for a workspace."""
    try:
        report = await license_manager.generate_usage_report(
            workspace_id=str(workspace_id),
            start_date=start_date,
            end_date=end_date
        )
        
        return report
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate usage report: {str(e)}"
        )


@router.get("/assets/stock/{asset_id}/license-info")
async def get_asset_license_info(
    asset_id: str,
    current_user: CurrentActiveUser,
):
    """Get detailed license information for a specific asset."""
    try:
        license_info = await license_manager._get_asset_license_info(asset_id)
        
        if not license_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="License information not found for asset"
            )
        
        return {
            "asset_id": asset_id,
            "license_type": license_info.license_type.value,
            "commercial_use": license_info.commercial_use,
            "editorial_use": license_info.editorial_use,
            "attribution_required": license_info.attribution_required,
            "redistribution_allowed": license_info.redistribution_allowed,
            "modification_allowed": license_info.modification_allowed,
            "print_circulation_limit": license_info.print_circulation_limit,
            "digital_circulation_limit": license_info.digital_circulation_limit,
            "geographic_restrictions": license_info.geographic_restrictions,
            "expiration_date": license_info.expiration_date.isoformat() if license_info.expiration_date else None,
            "purchase_date": license_info.purchase_date.isoformat() if license_info.purchase_date else None,
            "license_url": license_info.license_url,
            "license_text": license_info.license_text
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get license info: {str(e)}"
        )


@router.get("/workspaces/{workspace_id}/license-dashboard")
async def get_license_dashboard(
    workspace_id: UUID,
    current_user: CurrentActiveUser,
):
    """Get license management dashboard data for a workspace."""
    try:
        # Get compliance report
        compliance_report = await license_manager.check_workspace_compliance(str(workspace_id))
        
        # Get recent violations
        recent_violations = [
            v for v in license_manager.violations 
            if any(r.workspace_id == str(workspace_id) and r.asset_id == v.asset_id 
                  for r in license_manager.usage_records)
        ][-10:]  # Last 10 violations
        
        # Get expiration alerts
        expiration_alerts = await license_manager.get_license_expiration_alerts(
            workspace_id=str(workspace_id),
            days_ahead=30
        )
        
        # Calculate usage statistics
        workspace_usage = [
            r for r in license_manager.usage_records 
            if r.workspace_id == str(workspace_id)
        ]
        
        usage_by_type = {}
        for usage_type in UsageType:
            usage_by_type[usage_type.value] = len([
                r for r in workspace_usage if r.usage_type == usage_type
            ])
        
        return {
            "workspace_id": str(workspace_id),
            "dashboard_data": {
                "overview": {
                    "compliance_score": compliance_report["compliance_score"],
                    "total_violations": compliance_report["total_violations"],
                    "unresolved_violations": compliance_report["unresolved_violations"],
                    "total_assets_used": len(set([r.asset_id for r in workspace_usage])),
                    "total_usage_records": len(workspace_usage)
                },
                "recent_violations": [
                    {
                        "id": v.id,
                        "asset_id": v.asset_id,
                        "violation_type": v.violation_type,
                        "severity": v.severity.value,
                        "description": v.description,
                        "detected_at": v.detected_at.isoformat(),
                        "resolved": v.resolved
                    }
                    for v in recent_violations
                ],
                "expiration_alerts": expiration_alerts,
                "usage_statistics": {
                    "usage_by_type": usage_by_type,
                    "most_used_assets": compliance_report.get("most_violated_assets", [])
                },
                "recommendations": compliance_report.get("recommendations", [])
            },
            "last_updated": datetime.utcnow().isoformat()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get license dashboard: {str(e)}"
        )


class LicenseComplianceRequest(BaseModel):
    """Request model for license compliance checking."""
    asset_ids: List[str]
    usage_context: Optional[str] = None
    platform: Optional[str] = None
    circulation_estimate: Optional[int] = None
    geographic_region: Optional[str] = None


@router.post("/workspaces/{workspace_id}/check-bulk-compliance")
async def check_bulk_license_compliance(
    workspace_id: UUID,
    request: LicenseComplianceRequest,
    current_user: CurrentActiveUser,
):
    """Check license compliance for multiple assets at once."""
    try:
        results = []
        
        for asset_id in request.asset_ids:
            license_info = await license_manager._get_asset_license_info(asset_id)
            
            compliance_result = {
                "asset_id": asset_id,
                "compliant": True,
                "warnings": [],
                "violations": [],
                "license_type": license_info.license_type.value if license_info else "unknown"
            }
            
            if license_info:
                # Check commercial use
                if request.usage_context in ["commercial", "advertising", "merchandise"]:
                    if not license_info.commercial_use:
                        compliance_result["compliant"] = False
                        compliance_result["violations"].append({
                            "type": "commercial_use_violation",
                            "message": "Asset cannot be used for commercial purposes",
                            "recommendation": "Purchase commercial license"
                        })
                
                # Check circulation limits
                if request.circulation_estimate:
                    if license_info.print_circulation_limit and request.circulation_estimate > license_info.print_circulation_limit:
                        compliance_result["compliant"] = False
                        compliance_result["violations"].append({
                            "type": "circulation_limit_exceeded",
                            "message": f"Circulation estimate exceeds limit: {request.circulation_estimate} > {license_info.print_circulation_limit}",
                            "recommendation": "Purchase extended license"
                        })
                
                # Check geographic restrictions
                if license_info.geographic_restrictions and request.geographic_region:
                    if request.geographic_region in license_info.geographic_restrictions:
                        compliance_result["compliant"] = False
                        compliance_result["violations"].append({
                            "type": "geographic_restriction",
                            "message": f"Asset restricted in region: {request.geographic_region}",
                            "recommendation": "Use asset only in permitted regions"
                        })
                
                # Check expiration
                if license_info.expiration_date and datetime.utcnow() > license_info.expiration_date:
                    compliance_result["compliant"] = False
                    compliance_result["violations"].append({
                        "type": "expired_license",
                        "message": f"License expired on {license_info.expiration_date}",
                        "recommendation": "Renew license"
                    })
                
                # Add warnings for attribution requirements
                if license_info.attribution_required:
                    compliance_result["warnings"].append({
                        "type": "attribution_required",
                        "message": "Asset requires attribution",
                        "recommendation": "Include proper attribution in your design"
                    })
            
            results.append(compliance_result)
        
        # Summary statistics
        total_assets = len(results)
        compliant_assets = len([r for r in results if r["compliant"]])
        
        return {
            "workspace_id": str(workspace_id),
            "request_context": {
                "usage_context": request.usage_context,
                "platform": request.platform,
                "circulation_estimate": request.circulation_estimate,
                "geographic_region": request.geographic_region
            },
            "summary": {
                "total_assets": total_assets,
                "compliant_assets": compliant_assets,
                "non_compliant_assets": total_assets - compliant_assets,
                "compliance_rate": (compliant_assets / total_assets * 100) if total_assets > 0 else 100
            },
            "results": results,
            "checked_at": datetime.utcnow().isoformat()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check bulk compliance: {str(e)}"
        )


# Advanced Search System Endpoints

class AdvancedSearchRequest(BaseModel):
    """Advanced search request model with comprehensive filtering."""
    text: str
    asset_types: List[str] = ["photos", "icons", "graphics"]
    providers: List[str] = []
    
    # Visual filters
    orientation: Optional[str] = None
    color_palette: List[str] = []
    dominant_color: Optional[str] = None
    image_quality: Optional[str] = None
    
    # Content filters
    categories: List[str] = []
    tags: List[str] = []
    style: Optional[str] = None
    mood: Optional[str] = None
    
    # Technical filters
    min_width: Optional[int] = None
    max_width: Optional[int] = None
    min_height: Optional[int] = None
    max_height: Optional[int] = None
    file_formats: List[str] = []
    
    # License filters
    license_types: List[str] = []
    commercial_use: Optional[bool] = None
    editorial_use: Optional[bool] = None
    attribution_required: Optional[bool] = None
    
    # Search options
    include_similar: bool = False
    sort_by: str = "relevance"
    sort_order: str = "desc"
    limit: int = 20
    offset: int = 0
    
    # Advanced options
    exclude_keywords: List[str] = []
    exact_phrase: Optional[str] = None
    any_keywords: List[str] = []
    facial_recognition: Optional[bool] = None
    include_people: Optional[bool] = None


class AdvancedSearchResponse(BaseModel):
    """Advanced search response model."""
    results: List[Dict[str, Any]]
    total_results: int
    search_time_ms: float
    query_id: str
    providers_searched: List[str]
    suggestions: List[str] = []
    filters_applied: Dict[str, Any]
    ranking_info: Dict[str, Any]


@router.post("/assets/advanced-search", response_model=AdvancedSearchResponse)
async def advanced_asset_search(
    search_request: AdvancedSearchRequest,
    workspace_id: UUID,
    current_user: CurrentActiveUser,
    session_id: Optional[str] = None,
):
    """Perform advanced search across multiple asset providers with intelligent ranking."""
    try:
        # Create search query object
        search_query = SearchQuery(
            text=search_request.text,
            asset_types=search_request.asset_types,
            providers=search_request.providers,
            orientation=search_request.orientation,
            color_palette=search_request.color_palette,
            dominant_color=search_request.dominant_color,
            image_quality=search_request.image_quality,
            categories=search_request.categories,
            tags=search_request.tags,
            style=search_request.style,
            mood=search_request.mood,
            min_width=search_request.min_width,
            max_width=search_request.max_width,
            min_height=search_request.min_height,
            max_height=search_request.max_height,
            file_formats=search_request.file_formats,
            license_types=search_request.license_types,
            commercial_use=search_request.commercial_use,
            editorial_use=search_request.editorial_use,
            attribution_required=search_request.attribution_required,
            include_similar=search_request.include_similar,
            sort_by=search_request.sort_by,
            sort_order=search_request.sort_order,
            limit=search_request.limit,
            offset=search_request.offset,
            exclude_keywords=search_request.exclude_keywords,
            exact_phrase=search_request.exact_phrase,
            any_keywords=search_request.any_keywords,
            facial_recognition=search_request.facial_recognition,
            include_people=search_request.include_people
        )
        
        # Perform advanced search
        results, analytics = await advanced_search_service.search(
            query=search_query,
            user_id=current_user.id,
            workspace_id=str(workspace_id),
            session_id=session_id
        )
        
        # Convert results to response format
        formatted_results = []
        for result in results:
            formatted_results.append({
                "id": result.id,
                "url": result.url,
                "thumb_url": result.thumb_url,
                "title": result.title,
                "author": result.author,
                "source": result.source,
                "asset_type": result.asset_type,
                "width": result.width,
                "height": result.height,
                "orientation": result.orientation,
                "tags": result.tags,
                "categories": result.categories,
                "file_format": result.file_format,
                "license_type": result.license_type,
                "commercial_use": result.commercial_use,
                "attribution_required": result.attribution_required,
                "price": result.price,
                "download_url": result.download_url,
                "scores": {
                    "relevance": round(result.relevance_score, 3),
                    "popularity": round(result.popularity_score, 3),
                    "quality": round(result.quality_score, 3),
                    "final": round(result.final_score, 3)
                },
                "similar_asset_ids": result.similar_asset_ids,
                "download_count": result.download_count,
                "view_count": result.view_count,
                "like_count": result.like_count
            })
        
        return AdvancedSearchResponse(
            results=formatted_results,
            total_results=analytics.total_results,
            search_time_ms=analytics.search_time_ms,
            query_id=analytics.query_id,
            providers_searched=analytics.providers_searched,
            filters_applied=analytics.query_filters,
            ranking_info={
                "sort_by": search_request.sort_by,
                "sort_order": search_request.sort_order,
                "include_similar": search_request.include_similar
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Advanced search failed: {str(e)}"
        )


@router.get("/assets/search-suggestions")
async def get_search_suggestions(
    q: str,
    workspace_id: UUID,
    current_user: CurrentActiveUser,
    limit: int = 10,
):
    """Get intelligent search suggestions based on partial query."""
    try:
        if len(q.strip()) < 2:
            return {"suggestions": []}
        
        suggestions = await advanced_search_service.get_search_suggestions(
            partial_query=q,
            user_id=current_user.id,
            workspace_id=str(workspace_id),
            limit=limit
        )
        
        formatted_suggestions = []
        for suggestion in suggestions:
            formatted_suggestions.append({
                "text": suggestion.text,
                "type": suggestion.type,
                "popularity": suggestion.popularity,
                "confidence": round(suggestion.confidence, 2),
                "display_text": suggestion.text.title() if suggestion.type == "category" else suggestion.text
            })
        
        return {
            "suggestions": formatted_suggestions,
            "query": q,
            "total_suggestions": len(formatted_suggestions)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get search suggestions: {str(e)}"
        )


@router.post("/assets/search-interaction")
async def track_search_interaction(
    query_id: str,
    asset_id: str,
    interaction_type: str,
    current_user: CurrentActiveUser,
):
    """Track user interactions with search results for analytics."""
    try:
        await advanced_search_service.track_search_interaction(
            query_id=query_id,
            interaction_type=interaction_type,
            asset_id=asset_id
        )
        
        return {
            "query_id": query_id,
            "asset_id": asset_id,
            "interaction_type": interaction_type,
            "tracked_at": datetime.utcnow().isoformat(),
            "message": "Interaction tracked successfully"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to track interaction: {str(e)}"
        )


@router.get("/workspaces/{workspace_id}/search-analytics")
async def get_search_analytics(
    workspace_id: UUID,
    current_user: CurrentActiveUser,
    start_date: datetime = Query(...),
    end_date: datetime = Query(...),
):
    """Get comprehensive search analytics for a workspace."""
    try:
        analytics = await advanced_search_service.get_search_analytics(
            workspace_id=str(workspace_id),
            start_date=start_date,
            end_date=end_date
        )
        
        return {
            "workspace_id": str(workspace_id),
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "analytics": analytics,
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get search analytics: {str(e)}"
        )


class VisualSimilarityRequest(BaseModel):
    """Visual similarity search request."""
    asset_id: str
    asset_type: str = "photo"
    limit: int = 10
    similarity_threshold: float = 0.7


@router.post("/assets/visual-similarity-search")
async def visual_similarity_search(
    request: VisualSimilarityRequest,
    workspace_id: UUID,
    current_user: CurrentActiveUser,
):
    """Find visually similar assets to a given asset."""
    try:
        # Mock implementation - would use actual visual similarity algorithms
        similar_assets = []
        
        # Find similar assets based on the provided asset
        for i in range(min(request.limit, 5)):
            similar_assets.append({
                "id": f"similar_{request.asset_id}_{i}",
                "url": f"https://example.com/similar_{i}.jpg",
                "thumb_url": f"https://example.com/similar_{i}_thumb.jpg",
                "title": f"Similar asset {i+1}",
                "author": "Stock Provider",
                "source": "unsplash",
                "asset_type": request.asset_type,
                "similarity_score": round(0.9 - (i * 0.1), 2),
                "width": 1920,
                "height": 1080,
                "license_type": "free",
                "tags": ["similar", "visual", "match"]
            })
        
        return {
            "original_asset_id": request.asset_id,
            "similar_assets": similar_assets,
            "similarity_threshold": request.similarity_threshold,
            "total_found": len(similar_assets),
            "search_method": "visual_features",
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Visual similarity search failed: {str(e)}"
        )


class SearchFilterOptionsResponse(BaseModel):
    """Available search filter options."""
    categories: List[str]
    styles: List[str]
    moods: List[str]
    colors: List[str]
    orientations: List[str]
    file_formats: List[str]
    license_types: List[str]
    providers: List[str]


@router.get("/assets/search-filter-options", response_model=SearchFilterOptionsResponse)
async def get_search_filter_options(current_user: CurrentActiveUser):
    """Get available options for search filters."""
    try:
        return SearchFilterOptionsResponse(
            categories=[
                "business", "nature", "technology", "people", "abstract",
                "food", "travel", "sports", "education", "healthcare",
                "finance", "real-estate", "automotive", "fashion", "music"
            ],
            styles=[
                "modern", "vintage", "minimalist", "abstract", "realistic",
                "cartoon", "watercolor", "sketch", "professional", "casual",
                "artistic", "corporate", "creative", "elegant", "rustic"
            ],
            moods=[
                "happy", "serious", "energetic", "calm", "excited",
                "peaceful", "dramatic", "playful", "sophisticated", "fun",
                "inspiring", "motivational", "relaxing", "vibrant", "warm"
            ],
            colors=[
                "red", "blue", "green", "yellow", "purple", "orange",
                "pink", "brown", "black", "white", "gray", "multicolor"
            ],
            orientations=["landscape", "portrait", "square"],
            file_formats=["jpg", "png", "svg", "gif", "webp"],
            license_types=["free", "premium", "editorial", "extended"],
            providers=["unsplash", "pexels", "shutterstock", "iconify"]
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get filter options: {str(e)}"
        )


@router.get("/assets/trending-searches")
async def get_trending_searches(
    workspace_id: UUID,
    current_user: CurrentActiveUser,
    limit: int = 20,
    time_period: str = "7d",  # 1d, 7d, 30d
):
    """Get trending search queries for inspiration."""
    try:
        # Mock trending searches - would be based on actual analytics
        trending_searches = [
            {"query": "business meeting", "count": 156, "growth": 23.5},
            {"query": "modern office", "count": 142, "growth": 18.2},
            {"query": "nature landscape", "count": 134, "growth": 15.7},
            {"query": "technology icons", "count": 128, "growth": 12.3},
            {"query": "happy people", "count": 121, "growth": 9.8},
            {"query": "abstract background", "count": 115, "growth": 8.4},
            {"query": "food photography", "count": 108, "growth": 7.1},
            {"query": "minimalist design", "count": 102, "growth": 5.9},
            {"query": "team collaboration", "count": 98, "growth": 4.2},
            {"query": "digital marketing", "count": 94, "growth": 3.1}
        ]
        
        return {
            "workspace_id": str(workspace_id),
            "time_period": time_period,
            "trending_searches": trending_searches[:limit],
            "total_trending": len(trending_searches),
            "generated_at": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get trending searches: {str(e)}"
        )


class SavedSearchRequest(BaseModel):
    """Saved search request model."""
    name: str
    search_query: AdvancedSearchRequest
    notification_enabled: bool = False


@router.post("/assets/saved-searches")
async def save_search(
    request: SavedSearchRequest,
    workspace_id: UUID,
    current_user: CurrentActiveUser,
):
    """Save a search query for future use."""
    try:
        # Mock saved search implementation
        saved_search_id = f"saved_{current_user.id}_{len(request.name)}"
        
        return {
            "saved_search_id": saved_search_id,
            "name": request.name,
            "workspace_id": str(workspace_id),
            "user_id": current_user.id,
            "search_query": request.search_query.model_dump(),
            "notification_enabled": request.notification_enabled,
            "created_at": datetime.utcnow().isoformat(),
            "message": "Search saved successfully"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to save search: {str(e)}"
        )


@router.get("/assets/saved-searches")
async def get_saved_searches(
    workspace_id: UUID,
    current_user: CurrentActiveUser,
):
    """Get user's saved searches."""
    try:
        # Mock saved searches
        saved_searches = [
            {
                "saved_search_id": "saved_1",
                "name": "Business Photos",
                "search_query": {
                    "text": "business professional",
                    "categories": ["business"],
                    "orientation": "landscape"
                },
                "notification_enabled": True,
                "created_at": "2025-06-01T10:00:00Z",
                "last_used": "2025-06-09T08:30:00Z"
            },
            {
                "saved_search_id": "saved_2", 
                "name": "Modern Icons",
                "search_query": {
                    "text": "modern",
                    "asset_types": ["icons"],
                    "style": "minimalist"
                },
                "notification_enabled": False,
                "created_at": "2025-06-05T14:20:00Z",
                "last_used": "2025-06-08T16:45:00Z"
            }
        ]
        
        return {
            "workspace_id": str(workspace_id),
            "saved_searches": saved_searches,
            "total_saved": len(saved_searches)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get saved searches: {str(e)}"
        )


# =====================================================================
# ASSET RECOMMENDATION ENGINE ENDPOINTS
# =====================================================================

class DesignContextRequest(BaseModel):
    """Request model for design context analysis."""
    canvas_data: dict
    project_type: Optional[str] = None
    target_audience: Optional[str] = None
    usage_context: Optional[str] = None  # social_media, print, web, presentation
    brand_guidelines: Optional[dict] = None


class RecommendationRequest(BaseModel):
    """Request model for asset recommendations."""
    canvas_data: dict
    project_type: Optional[str] = None
    target_audience: Optional[str] = None
    usage_context: Optional[str] = None
    brand_guidelines: Optional[dict] = None
    limit: Optional[int] = 10


class RecommendationFeedbackRequest(BaseModel):
    """Request model for recommendation feedback."""
    recommendation_id: str
    feedback_type: str  # accepted, rejected, ignored, modified
    feedback_score: float  # 0.0 to 1.0
    usage_outcome: Optional[str] = None


@router.post("/assets/recommendations")
async def get_asset_recommendations(
    request: RecommendationRequest,
    workspace_id: UUID,
    current_user: CurrentActiveUser,
):
    """Get AI-powered asset recommendations based on design context."""
    try:
        from langflow.services.stock_assets.asset_recommendation_engine import (
            asset_recommendation_engine,
            DesignContext,
            RecommendationContext
        )
        
        # Analyze design context
        design_context = await asset_recommendation_engine._analyze_design_context(
            request.canvas_data
        )
        
        # Create recommendation context
        user_preferences = await asset_recommendation_engine._get_user_preferences(
            str(current_user.id), 
            str(workspace_id)
        )
        
        recommendation_context = RecommendationContext(
            design_context=design_context,
            user_preferences=user_preferences,
            project_type=request.project_type,
            target_audience=request.target_audience,
            brand_guidelines=request.brand_guidelines or {},
            usage_context=request.usage_context
        )
        
        # Get recommendations
        recommendations = await asset_recommendation_engine.get_recommendations(
            design_context=design_context,
            user_id=str(current_user.id),
            workspace_id=str(workspace_id),
            recommendation_context=recommendation_context,
            limit=request.limit
        )
        
        # Convert to response format
        recommendation_data = []
        for rec in recommendations:
            recommendation_data.append({
                "recommendation_id": rec.asset_id,
                "asset": {
                    "id": rec.asset_id,
                    "type": rec.asset_type,
                    "source": rec.source,
                    "url": rec.url,
                    "thumb_url": rec.thumb_url,
                    "title": rec.title,
                    "author": rec.author,
                    "tags": rec.tags,
                    "categories": rec.categories,
                    "license_type": rec.license_type,
                    "commercial_use": rec.commercial_use,
                    "attribution_required": rec.attribution_required,
                    "price": rec.price
                },
                "recommendation_score": rec.relevance_score,
                "confidence_score": rec.confidence_score,
                "reasoning": rec.reasoning,
                "placement_suggestions": rec.placement_suggestions,
                "scores": {
                    "style_match": rec.style_match_score,
                    "color_match": rec.color_match_score,
                    "context_match": rec.context_match_score
                }
            })
        
        return {
            "workspace_id": str(workspace_id),
            "recommendations": recommendation_data,
            "design_analysis": {
                "style": design_context.design_style,
                "mood": design_context.mood,
                "theme": design_context.theme,
                "missing_elements": design_context.missing_elements,
                "color_palette": design_context.color_palette,
                "element_counts": {
                    "images": design_context.image_count,
                    "text": design_context.text_element_count,
                    "shapes": design_context.shape_count,
                    "icons": design_context.icon_count
                }
            },
            "recommendation_context": {
                "project_type": request.project_type,
                "usage_context": request.usage_context,
                "target_audience": request.target_audience
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get recommendations: {str(e)}"
        )


@router.post("/assets/recommendations/analyze-context")
async def analyze_design_context(
    request: DesignContextRequest,
    current_user: CurrentActiveUser,
):
    """Analyze design context without generating recommendations."""
    try:
        from langflow.services.stock_assets.asset_recommendation_engine import (
            asset_recommendation_engine
        )
        
        # Analyze design context
        design_context = await asset_recommendation_engine._analyze_design_context(
            request.canvas_data
        )
        
        return {
            "design_analysis": {
                "style": design_context.design_style,
                "mood": design_context.mood, 
                "theme": design_context.theme,
                "layout_type": design_context.layout_type,
                "missing_elements": design_context.missing_elements,
                "color_palette": design_context.color_palette,
                "dominant_colors": design_context.dominant_colors,
                "element_counts": {
                    "images": design_context.image_count,
                    "text": design_context.text_element_count,
                    "shapes": design_context.shape_count,
                    "icons": design_context.icon_count
                },
                "design_metrics": {
                    "overall_balance": design_context.overall_balance,
                    "color_harmony_score": design_context.color_harmony_score,
                    "visual_weight_distribution": design_context.visual_weight_distribution
                },
                "text_content": design_context.text_content
            },
            "recommendations_preview": {
                "suggested_asset_types": ["photos", "icons", "graphics"],
                "suggested_improvements": design_context.missing_elements,
                "estimated_recommendations": min(len(design_context.missing_elements) * 3, 20)
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze design context: {str(e)}"
        )


@router.post("/assets/recommendations/feedback")
async def submit_recommendation_feedback(
    request: RecommendationFeedbackRequest,
    workspace_id: UUID,
    current_user: CurrentActiveUser,
):
    """Submit feedback on asset recommendations for learning."""
    try:
        from langflow.services.stock_assets.asset_recommendation_engine import (
            asset_recommendation_engine
        )
        
        await asset_recommendation_engine.submit_feedback(
            recommendation_id=request.recommendation_id,
            user_id=str(current_user.id),
            workspace_id=str(workspace_id),
            feedback_type=request.feedback_type,
            feedback_score=request.feedback_score,
            usage_outcome=request.usage_outcome
        )
        
        return {
            "success": True,
            "message": "Feedback submitted successfully",
            "recommendation_id": request.recommendation_id,
            "feedback_type": request.feedback_type
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to submit feedback: {str(e)}"
        )


@router.get("/workspaces/{workspace_id}/recommendation-preferences")
async def get_user_recommendation_preferences(
    workspace_id: UUID,
    current_user: CurrentActiveUser,
):
    """Get user's recommendation preferences based on historical behavior."""
    try:
        from langflow.services.stock_assets.asset_recommendation_engine import (
            asset_recommendation_engine
        )
        
        preferences = await asset_recommendation_engine._get_user_preferences(
            str(current_user.id),
            str(workspace_id)
        )
        
        return {
            "workspace_id": str(workspace_id),
            "user_id": str(current_user.id),
            "preferences": preferences,
            "learning_status": {
                "total_feedback_received": len([
                    fb for fb in asset_recommendation_engine.user_feedback 
                    if fb.user_id == str(current_user.id)
                ]),
                "recommendations_generated": len([
                    rec for rec in asset_recommendation_engine.recommendation_history
                    if hasattr(rec, 'user_id') and rec.user_id == str(current_user.id)
                ]),
                "personalization_level": "high" if len(preferences.get("preferred_sources", [])) > 2 else "medium"
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get recommendation preferences: {str(e)}"
        )


@router.post("/assets/recommendations/real-time")
async def get_real_time_recommendations(
    request: RecommendationRequest,
    workspace_id: UUID,
    current_user: CurrentActiveUser,
):
    """Get real-time asset recommendations as user modifies design."""
    try:
        from langflow.services.stock_assets.asset_recommendation_engine import (
            asset_recommendation_engine,
            DesignContext,
            RecommendationContext
        )
        
        # Quick analysis for real-time updates
        design_context = await asset_recommendation_engine._analyze_design_context(
            request.canvas_data
        )
        
        # Generate quick recommendations with reduced complexity
        design_needs = await asset_recommendation_engine._analyze_design_needs(design_context)
        
        # Generate fewer, faster recommendations for real-time updates
        search_queries = await asset_recommendation_engine._generate_search_queries(
            design_context, 
            design_needs, 
            RecommendationContext(design_context=design_context)
        )
        
        # Limit to top 2 queries for speed
        quick_queries = search_queries[:2]
        candidates = await asset_recommendation_engine._search_candidate_assets(quick_queries)
        
        # Quick scoring
        quick_recommendations = []
        for candidate in candidates[:6]:  # Limit for speed
            try:
                quick_rec = {
                    "asset_id": candidate.id,
                    "asset_type": candidate.asset_type,
                    "url": candidate.thumb_url,
                    "title": candidate.title,
                    "relevance_score": getattr(candidate, 'relevance_score', 0.5),
                    "quick_reasoning": f"Matches {design_context.design_style or 'your'} style"
                }
                quick_recommendations.append(quick_rec)
            except:
                continue
        
        return {
            "workspace_id": str(workspace_id),
            "quick_recommendations": quick_recommendations,
            "design_status": {
                "completeness_score": min((design_context.image_count + design_context.text_element_count) / 5, 1.0),
                "style_consistency": design_context.color_harmony_score,
                "suggested_next_steps": design_context.missing_elements[:3]
            },
            "real_time": True
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get real-time recommendations: {str(e)}"
        )


@router.get("/workspaces/{workspace_id}/recommendation-analytics")
async def get_recommendation_analytics(
    workspace_id: UUID,
    current_user: CurrentActiveUser,
    days: int = Query(30, description="Number of days to analyze")
):
    """Get analytics on recommendation usage and effectiveness."""
    try:
        from langflow.services.stock_assets.asset_recommendation_engine import (
            asset_recommendation_engine
        )
        
        # Get user's recommendation history
        user_recommendations = [
            rec for rec in asset_recommendation_engine.recommendation_history
            if hasattr(rec, 'user_id') and rec.user_id == str(current_user.id)
        ]
        
        user_feedback = [
            fb for fb in asset_recommendation_engine.user_feedback
            if fb.user_id == str(current_user.id)
        ]
        
        # Calculate analytics
        total_recommendations = len(user_recommendations)
        total_feedback = len(user_feedback)
        
        positive_feedback = [fb for fb in user_feedback if fb.feedback_score > 0.6]
        acceptance_rate = (len(positive_feedback) / total_feedback * 100) if total_feedback > 0 else 0
        
        # Asset type breakdown
        asset_type_counts = {}
        for rec in user_recommendations:
            asset_type_counts[rec.asset_type] = asset_type_counts.get(rec.asset_type, 0) + 1
        
        # Source preference analysis
        source_usage = {}
        for feedback in positive_feedback:
            rec = next((r for r in user_recommendations if r.asset_id == feedback.recommendation_id), None)
            if rec:
                source_usage[rec.source] = source_usage.get(rec.source, 0) + 1
        
        return {
            "workspace_id": str(workspace_id),
            "analytics_period_days": days,
            "recommendation_stats": {
                "total_recommendations_received": total_recommendations,
                "total_feedback_given": total_feedback,
                "acceptance_rate_percentage": round(acceptance_rate, 1),
                "avg_recommendation_score": round(
                    sum(rec.relevance_score for rec in user_recommendations) / len(user_recommendations), 2
                ) if user_recommendations else 0
            },
            "usage_patterns": {
                "asset_type_preferences": asset_type_counts,
                "preferred_sources": source_usage,
                "most_successful_styles": ["modern", "minimalist", "professional"],  # Mock data
                "peak_usage_times": ["Tuesday 2-4 PM", "Wednesday 10-12 AM"]  # Mock data
            },
            "recommendation_effectiveness": {
                "learning_progress": "high" if total_feedback > 10 else "medium",
                "personalization_accuracy": min(acceptance_rate + 20, 95),  # Mock calculation
                "trend": "improving" if acceptance_rate > 60 else "stable"
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get recommendation analytics: {str(e)}"
        )


# =====================================================================
# USER ASSET SHARING ENDPOINTS
# =====================================================================

class WorkspaceLibraryRequest(BaseModel):
    """Request model for creating workspace asset library."""
    name: str
    description: Optional[str] = None
    storage_quota_mb: int = 5000
    settings: Optional[Dict[str, Any]] = None


class AssetUploadMetadata(BaseModel):
    """Metadata for asset upload."""
    description: Optional[str] = None
    tags: List[str] = []
    categories: List[str] = []
    custom_metadata: Optional[Dict[str, Any]] = None


class AssetSharingRequest(BaseModel):
    """Request model for asset sharing."""
    share_scope: str  # private, workspace, team, public
    permissions: Optional[Dict[str, str]] = None
    team_permissions: Optional[Dict[str, str]] = None


class AssetVersionRequest(BaseModel):
    """Request model for creating asset version."""
    change_summary: str
    change_details: Optional[str] = None


class AssetCommentRequest(BaseModel):
    """Request model for adding asset comments."""
    comment_text: str
    reply_to: Optional[str] = None


class AssetApprovalSubmissionRequest(BaseModel):
    """Request model for submitting asset for approval."""
    reviewer_ids: List[str]
    approval_type: str = "publish"
    request_notes: Optional[str] = None
    priority: str = "normal"


class AssetApprovalDecisionRequest(BaseModel):
    """Request model for approval decision."""
    approved: bool
    notes: Optional[str] = None


@router.post("/workspaces/{workspace_id}/asset-library")
async def create_workspace_asset_library(
    workspace_id: UUID,
    request: WorkspaceLibraryRequest,
    current_user: CurrentActiveUser,
):
    """Create a new workspace asset library."""
    try:
        from langflow.services.stock_assets.user_asset_sharing_service import (
            user_asset_sharing_service
        )
        
        library = await user_asset_sharing_service.create_workspace_library(
            workspace_id=str(workspace_id),
            name=request.name,
            description=request.description,
            storage_quota_mb=request.storage_quota_mb,
            settings=request.settings
        )
        
        return {
            "workspace_id": str(workspace_id),
            "library": {
                "name": library.name,
                "description": library.description,
                "storage_quota_mb": library.storage_quota_mb,
                "used_storage_mb": library.used_storage_mb,
                "max_file_size_mb": library.max_file_size_mb,
                "allowed_file_types": library.allowed_file_types,
                "auto_approval_enabled": library.auto_approval_enabled,
                "default_permissions": {k: v.value for k, v in library.default_permissions.items()},
                "approval_workflow": library.approval_workflow,
                "created_at": library.created_at.isoformat(),
                "settings": library.settings
            },
            "message": "Workspace asset library created successfully"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create workspace library: {str(e)}"
        )


@router.post("/workspaces/{workspace_id}/assets/upload")
async def upload_user_asset(
    workspace_id: UUID,
    current_user: CurrentActiveUser,
    file: UploadFile = File(...),
    asset_type: str = Form(...),
    metadata: Optional[str] = Form(None),
):
    """Upload a new user asset to the workspace library."""
    try:
        from langflow.services.stock_assets.user_asset_sharing_service import (
            user_asset_sharing_service
        )
        
        # Read file data
        file_data = await file.read()
        
        # Parse metadata if provided
        parsed_metadata = None
        if metadata:
            try:
                parsed_metadata = json.loads(metadata)
            except json.JSONDecodeError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid metadata JSON format"
                )
        
        # Upload asset
        asset = await user_asset_sharing_service.upload_asset(
            file_data=file_data,
            filename=file.filename,
            asset_type=asset_type,
            workspace_id=str(workspace_id),
            user_id=str(current_user.id),
            metadata=parsed_metadata
        )
        
        return {
            "asset": {
                "id": asset.id,
                "name": asset.name,
                "description": asset.description,
                "asset_type": asset.asset_type,
                "file_format": asset.file_format,
                "file_size": asset.file_size,
                "width": asset.width,
                "height": asset.height,
                "uploaded_by": asset.uploaded_by,
                "uploaded_at": asset.uploaded_at.isoformat(),
                "workspace_id": asset.workspace_id,
                "share_scope": asset.share_scope.value,
                "status": asset.status.value,
                "version": asset.version,
                "tags": asset.tags,
                "categories": asset.categories,
                "color_palette": asset.color_palette
            },
            "message": "Asset uploaded successfully"
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload asset: {str(e)}"
        )


@router.post("/assets/{asset_id}/share")
async def share_user_asset(
    asset_id: str,
    request: AssetSharingRequest,
    current_user: CurrentActiveUser,
):
    """Share an asset with specified scope and permissions."""
    try:
        from langflow.services.stock_assets.user_asset_sharing_service import (
            user_asset_sharing_service,
            ShareScope,
            AssetPermission
        )
        
        # Convert string permissions to enums
        permissions = {}
        if request.permissions:
            for user_id, perm_str in request.permissions.items():
                permissions[user_id] = AssetPermission(perm_str)
        
        team_permissions = {}
        if request.team_permissions:
            for team_id, perm_str in request.team_permissions.items():
                team_permissions[team_id] = AssetPermission(perm_str)
        
        asset = await user_asset_sharing_service.share_asset(
            asset_id=asset_id,
            user_id=str(current_user.id),
            share_scope=ShareScope(request.share_scope),
            permissions=permissions,
            team_permissions=team_permissions
        )
        
        return {
            "asset_id": asset_id,
            "share_scope": asset.share_scope.value,
            "permissions": {k: v.value for k, v in asset.permissions.items()},
            "team_permissions": {k: v.value for k, v in asset.team_permissions.items()},
            "shared_by": str(current_user.id),
            "shared_at": datetime.utcnow().isoformat(),
            "message": "Asset shared successfully"
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to share asset: {str(e)}"
        )


@router.post("/assets/{asset_id}/versions")
async def create_asset_version(
    asset_id: str,
    request: AssetVersionRequest,
    current_user: CurrentActiveUser,
    file: UploadFile = File(...),
):
    """Create a new version of an existing asset."""
    try:
        from langflow.services.stock_assets.user_asset_sharing_service import (
            user_asset_sharing_service
        )
        
        # Read file data
        file_data = await file.read()
        
        # Create version
        version = await user_asset_sharing_service.create_asset_version(
            asset_id=asset_id,
            user_id=str(current_user.id),
            file_data=file_data,
            change_summary=request.change_summary,
            change_details=request.change_details
        )
        
        return {
            "version": {
                "version_id": version.version_id,
                "asset_id": version.asset_id,
                "version_number": version.version_number,
                "created_by": version.created_by,
                "created_at": version.created_at.isoformat(),
                "file_size": version.file_size,
                "change_summary": version.change_summary,
                "change_details": version.change_details,
                "is_current": version.is_current
            },
            "message": "Asset version created successfully"
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create asset version: {str(e)}"
        )


@router.get("/assets/{asset_id}/versions")
async def get_asset_versions(
    asset_id: str,
    current_user: CurrentActiveUser,
):
    """Get version history for an asset."""
    try:
        from langflow.services.stock_assets.user_asset_sharing_service import (
            user_asset_sharing_service,
            AssetPermission
        )
        
        asset = user_asset_sharing_service.user_assets.get(asset_id)
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        # Check permissions
        if not await user_asset_sharing_service._has_permission(asset, str(current_user.id), AssetPermission.VIEW):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view asset versions"
            )
        
        versions = user_asset_sharing_service.asset_versions.get(asset_id, [])
        
        version_data = []
        for version in versions:
            version_data.append({
                "version_id": version.version_id,
                "version_number": version.version_number,
                "created_by": version.created_by,
                "created_at": version.created_at.isoformat(),
                "file_size": version.file_size,
                "change_summary": version.change_summary,
                "change_details": version.change_details,
                "is_current": version.is_current
            })
        
        return {
            "asset_id": asset_id,
            "versions": sorted(version_data, key=lambda x: x["created_at"], reverse=True),
            "total_versions": len(version_data)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get asset versions: {str(e)}"
        )


# Initialize Collections Service
collections_service = CollectionsService()


# Collections and Favorites Endpoints

class CollectionCreateRequest(BaseModel):
    """Request model for creating a collection."""
    name: str
    description: str = ""
    category: str = "general"
    is_public: bool = False
    tags: List[str] = []
    color_theme: str = "#3B82F6"
    icon: str = "folder"


class CollectionResponse(BaseModel):
    """Response model for collection data."""
    id: str
    name: str
    description: str
    category: str
    is_public: bool
    is_shared: bool
    tags: List[str]
    color_theme: str
    icon: str
    asset_count: int
    created_at: str
    updated_at: str
    access_count: int


class FavoriteCreateRequest(BaseModel):
    """Request model for adding to favorites."""
    asset_id: str
    asset_type: str = "stock"
    asset_source: str = ""
    notes: str = ""
    tags: List[str] = []
    priority: str = "normal"


class FavoriteResponse(BaseModel):
    """Response model for favorite data."""
    id: str
    asset_id: str
    asset_type: str
    asset_source: str
    notes: str
    tags: List[str]
    priority: str
    added_at: str


@router.post("/workspaces/{workspace_id}/collections", response_model=CollectionResponse)
async def create_collection(
    workspace_id: str,
    request: CollectionCreateRequest,
    current_user: CurrentActiveUser
):
    """Create a new asset collection."""
    try:
        collection = await collections_service.create_collection(
            user_id=str(current_user.id),
            workspace_id=workspace_id,
            name=request.name,
            description=request.description,
            category=request.category,
            is_public=request.is_public,
            tags=request.tags,
            color_theme=request.color_theme,
            icon=request.icon
        )
        
        return CollectionResponse(
            id=collection.id,
            name=collection.name,
            description=collection.description,
            category=collection.category,
            is_public=collection.is_public,
            is_shared=collection.is_shared,
            tags=collection.tags,
            color_theme=collection.color_theme,
            icon=collection.icon,
            asset_count=len(collection.asset_ids),
            created_at=collection.created_at.isoformat(),
            updated_at=collection.updated_at.isoformat(),
            access_count=collection.access_count
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create collection: {str(e)}"
        )


@router.get("/workspaces/{workspace_id}/collections", response_model=List[CollectionResponse])
async def get_collections(
    workspace_id: str,
    current_user: CurrentActiveUser,
    category: Optional[str] = Query(None),
    include_shared: bool = Query(True)
):
    """Get all collections for a user in a workspace."""
    try:
        collections = await collections_service.get_user_collections(
            user_id=str(current_user.id),
            workspace_id=workspace_id,
            category=category,
            include_shared=include_shared
        )
        
        return [
            CollectionResponse(
                id=c.id,
                name=c.name,
                description=c.description,
                category=c.category,
                is_public=c.is_public,
                is_shared=c.is_shared,
                tags=c.tags,
                color_theme=c.color_theme,
                icon=c.icon,
                asset_count=len(c.asset_ids),
                created_at=c.created_at.isoformat(),
                updated_at=c.updated_at.isoformat(),
                access_count=c.access_count
            )
            for c in collections
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get collections: {str(e)}"
        )


@router.post("/collections/{collection_id}/assets/{asset_id}")
async def add_asset_to_collection(
    collection_id: str,
    asset_id: str,
    current_user: CurrentActiveUser
):
    """Add an asset to a collection."""
    try:
        success = await collections_service.add_asset_to_collection(
            collection_id=collection_id,
            asset_id=asset_id,
            user_id=str(current_user.id)
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Collection not found or access denied"
            )
        
        return {"message": "Asset added to collection successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add asset to collection: {str(e)}"
        )


@router.delete("/collections/{collection_id}/assets/{asset_id}")
async def remove_asset_from_collection(
    collection_id: str,
    asset_id: str,
    current_user: CurrentActiveUser
):
    """Remove an asset from a collection."""
    try:
        success = await collections_service.remove_asset_from_collection(
            collection_id=collection_id,
            asset_id=asset_id,
            user_id=str(current_user.id)
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Collection not found or access denied"
            )
        
        return {"message": "Asset removed from collection successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to remove asset from collection: {str(e)}"
        )


@router.post("/workspaces/{workspace_id}/favorites", response_model=FavoriteResponse)
async def add_to_favorites(
    workspace_id: str,
    request: FavoriteCreateRequest,
    current_user: CurrentActiveUser
):
    """Add an asset to favorites."""
    try:
        favorite = await collections_service.add_to_favorites(
            user_id=str(current_user.id),
            workspace_id=workspace_id,
            asset_id=request.asset_id,
            asset_type=request.asset_type,
            asset_source=request.asset_source,
            notes=request.notes,
            tags=request.tags,
            priority=request.priority
        )
        
        return FavoriteResponse(
            id=favorite.id,
            asset_id=favorite.asset_id,
            asset_type=favorite.asset_type,
            asset_source=favorite.asset_source,
            notes=favorite.notes,
            tags=favorite.tags,
            priority=favorite.priority,
            added_at=favorite.added_at.isoformat()
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add to favorites: {str(e)}"
        )


@router.delete("/workspaces/{workspace_id}/favorites/{asset_id}")
async def remove_from_favorites(
    workspace_id: str,
    asset_id: str,
    current_user: CurrentActiveUser
):
    """Remove an asset from favorites."""
    try:
        success = await collections_service.remove_from_favorites(
            user_id=str(current_user.id),
            workspace_id=workspace_id,
            asset_id=asset_id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Favorite not found"
            )
        
        return {"message": "Asset removed from favorites successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to remove from favorites: {str(e)}"
        )


@router.get("/workspaces/{workspace_id}/favorites", response_model=List[FavoriteResponse])
async def get_favorites(
    workspace_id: str,
    current_user: CurrentActiveUser,
    asset_type: Optional[str] = Query(None),
    priority: Optional[str] = Query(None)
):
    """Get user's favorite assets."""
    try:
        favorites = await collections_service.get_user_favorites(
            user_id=str(current_user.id),
            workspace_id=workspace_id,
            asset_type=asset_type,
            priority=priority
        )
        
        return [
            FavoriteResponse(
                id=f.id,
                asset_id=f.asset_id,
                asset_type=f.asset_type,
                asset_source=f.asset_source,
                notes=f.notes,
                tags=f.tags,
                priority=f.priority,
                added_at=f.added_at.isoformat()
            )
            for f in favorites
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get favorites: {str(e)}"
        )


class CollectionShareRequest(BaseModel):
    """Request model for sharing a collection."""
    shared_with_user_id: Optional[str] = None
    shared_with_workspace_id: Optional[str] = None
    permission_level: str = "view"
    share_scope: str = "user"
    expires_at: Optional[str] = None


@router.post("/collections/{collection_id}/share")
async def share_collection(
    collection_id: str,
    request: CollectionShareRequest,
    current_user: CurrentActiveUser
):
    """Share a collection with another user or workspace."""
    try:
        expires_at = None
        if request.expires_at:
            expires_at = datetime.fromisoformat(request.expires_at)
        
        share = await collections_service.share_collection(
            collection_id=collection_id,
            shared_by_user_id=str(current_user.id),
            shared_with_user_id=request.shared_with_user_id,
            shared_with_workspace_id=request.shared_with_workspace_id,
            permission_level=request.permission_level,
            share_scope=request.share_scope,
            expires_at=expires_at
        )
        
        return {
            "share_id": share.id,
            "message": "Collection shared successfully",
            "shared_at": share.shared_at.isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to share collection: {str(e)}"
        )


@router.get("/workspaces/{workspace_id}/shared-collections", response_model=List[CollectionResponse])
async def get_shared_collections(
    workspace_id: str,
    current_user: CurrentActiveUser
):
    """Get collections shared with a user."""
    try:
        collections = await collections_service.get_shared_collections(
            user_id=str(current_user.id),
            workspace_id=workspace_id
        )
        
        return [
            CollectionResponse(
                id=c.id,
                name=c.name,
                description=c.description,
                category=c.category,
                is_public=c.is_public,
                is_shared=c.is_shared,
                tags=c.tags,
                color_theme=c.color_theme,
                icon=c.icon,
                asset_count=len(c.asset_ids),
                created_at=c.created_at.isoformat(),
                updated_at=c.updated_at.isoformat(),
                access_count=c.access_count
            )
            for c in collections
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get shared collections: {str(e)}"
        )


class CollectionOrganizeRequest(BaseModel):
    """Request model for organizing collections."""
    collection_orders: List[Dict[str, Any]]


@router.post("/workspaces/{workspace_id}/collections/organize")
async def organize_collections(
    workspace_id: str,
    request: CollectionOrganizeRequest,
    current_user: CurrentActiveUser
):
    """Organize collections by updating their sort order."""
    try:
        success = await collections_service.organize_collections(
            user_id=str(current_user.id),
            workspace_id=workspace_id,
            collection_orders=request.collection_orders
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to organize collections"
            )
        
        return {"message": "Collections organized successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to organize collections: {str(e)}"
        )


@router.get("/collections/{collection_id}/export")
async def export_collection(
    collection_id: str,
    current_user: CurrentActiveUser,
    export_format: str = Query("json")
):
    """Export a collection in the specified format."""
    try:
        export_data = await collections_service.export_collection(
            collection_id=collection_id,
            user_id=str(current_user.id),
            export_format=export_format
        )
        
        if not export_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Collection not found or access denied"
            )
        
        return export_data
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export collection: {str(e)}"
        )


class CollectionImportRequest(BaseModel):
    """Request model for importing a collection."""
    import_data: Dict[str, Any]
    merge_strategy: str = "create_new"


@router.post("/workspaces/{workspace_id}/collections/import", response_model=CollectionResponse)
async def import_collection(
    workspace_id: str,
    request: CollectionImportRequest,
    current_user: CurrentActiveUser
):
    """Import a collection from exported data."""
    try:
        collection = await collections_service.import_collection(
            user_id=str(current_user.id),
            workspace_id=workspace_id,
            import_data=request.import_data,
            merge_strategy=request.merge_strategy
        )
        
        return CollectionResponse(
            id=collection.id,
            name=collection.name,
            description=collection.description,
            category=collection.category,
            is_public=collection.is_public,
            is_shared=collection.is_shared,
            tags=collection.tags,
            color_theme=collection.color_theme,
            icon=collection.icon,
            asset_count=len(collection.asset_ids),
            created_at=collection.created_at.isoformat(),
            updated_at=collection.updated_at.isoformat(),
            access_count=collection.access_count
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to import collection: {str(e)}"
        )


@router.get("/collections/{collection_id}/analytics")
async def get_collection_analytics(
    collection_id: str,
    current_user: CurrentActiveUser,
    date_range: int = Query(30)
):
    """Get analytics for a collection."""
    try:
        analytics = await collections_service.get_collection_analytics(
            collection_id=collection_id,
            user_id=str(current_user.id),
            date_range=date_range
        )
        
        if not analytics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Collection not found or access denied"
            )
        
        return analytics
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get collection analytics: {str(e)}"
        )


@router.get("/workspaces/{workspace_id}/collections/analytics")
async def get_workspace_collection_analytics(
    workspace_id: str,
    current_user: CurrentActiveUser,
    date_range: int = Query(30)
):
    """Get workspace-level collection analytics."""
    try:
        analytics = await collections_service.get_workspace_collection_analytics(
            workspace_id=workspace_id,
            user_id=str(current_user.id),
            date_range=date_range
        )
        
        return analytics
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get workspace collection analytics: {str(e)}"
        )


@router.get("/workspaces/{workspace_id}/collections/search", response_model=List[CollectionResponse])
async def search_collections(
    workspace_id: str,
    current_user: CurrentActiveUser,
    query: str = Query(...),
    category: Optional[str] = Query(None),
    is_public: Optional[bool] = Query(None),
    has_assets: Optional[bool] = Query(None)
):
    """Search collections by name, description, or tags."""
    try:
        filters = {}
        if category:
            filters['category'] = category
        if is_public is not None:
            filters['is_public'] = is_public
        if has_assets is not None:
            filters['has_assets'] = has_assets
        
        collections = await collections_service.search_collections(
            user_id=str(current_user.id),
            workspace_id=workspace_id,
            query=query,
            filters=filters
        )
        
        return [
            CollectionResponse(
                id=c.id,
                name=c.name,
                description=c.description,
                category=c.category,
                is_public=c.is_public,
                is_shared=c.is_shared,
                tags=c.tags,
                color_theme=c.color_theme,
                icon=c.icon,
                asset_count=len(c.asset_ids),
                created_at=c.created_at.isoformat(),
                updated_at=c.updated_at.isoformat(),
                access_count=c.access_count
            )
            for c in collections
        ]
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search collections: {str(e)}"
        )
