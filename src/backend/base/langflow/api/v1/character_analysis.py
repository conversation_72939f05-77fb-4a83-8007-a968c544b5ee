"""Character analysis and reporting API endpoints."""

from typing import Any

from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session

from langflow.api.utils import CurrentActiveUser
from langflow.services.database.models.character.analysis_service import CharacterAnalysisService
from langflow.services.deps import get_session

router = APIRouter(prefix="/character-analysis", tags=["Character Analysis"])


@router.get("/books/{book_id}/usage-statistics")
async def get_character_usage_statistics(
    book_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> dict[str, Any]:
    """Get comprehensive character usage statistics for a book."""
    try:
        analysis_service = CharacterAnalysisService(session)
        return await analysis_service.generate_character_usage_statistics(
            book_id=book_id,
            workspace_id=workspace_id,
            user_id=current_user.id
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate usage statistics: {str(e)}")


@router.get("/characters/{character_id}/development-report")
async def get_character_development_report(
    character_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> dict[str, Any]:
    """Get detailed character development report."""
    try:
        analysis_service = CharacterAnalysisService(session)
        return await analysis_service.generate_character_development_report(
            character_id=character_id,
            workspace_id=workspace_id,
            user_id=current_user.id
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate development report: {str(e)}")


@router.get("/books/{book_id}/relationship-complexity")
async def get_relationship_complexity_analysis(
    book_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> dict[str, Any]:
    """Get relationship complexity analysis for a book."""
    try:
        analysis_service = CharacterAnalysisService(session)
        return await analysis_service.analyze_relationship_complexity(
            book_id=book_id,
            workspace_id=workspace_id,
            user_id=current_user.id
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to analyze relationship complexity: {str(e)}")


@router.get("/books/{book_id}/arc-completeness")
async def get_character_arc_completeness_metrics(
    book_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> dict[str, Any]:
    """Get character arc completeness metrics for a book."""
    try:
        analysis_service = CharacterAnalysisService(session)
        return await analysis_service.calculate_character_arc_completeness_metrics(
            book_id=book_id,
            workspace_id=workspace_id,
            user_id=current_user.id
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to calculate arc completeness: {str(e)}")


@router.get("/books/{book_id}/balance-recommendations")
async def get_character_balance_recommendations(
    book_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> dict[str, Any]:
    """Get character balance recommendations for a book."""
    try:
        analysis_service = CharacterAnalysisService(session)
        return await analysis_service.generate_character_balance_recommendations(
            book_id=book_id,
            workspace_id=workspace_id,
            user_id=current_user.id
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate balance recommendations: {str(e)}")


@router.get("/books/{book_id}/export-character-sheets")
async def export_character_sheets(
    book_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
    export_format: str = "json",
) -> dict[str, Any]:
    """Export character sheets in various formats."""
    if export_format not in ["json", "csv", "pdf"]:
        raise HTTPException(status_code=400, detail="Invalid export format. Supported formats: json, csv, pdf")
    
    try:
        analysis_service = CharacterAnalysisService(session)
        return await analysis_service.export_character_sheets(
            book_id=book_id,
            workspace_id=workspace_id,
            user_id=current_user.id,
            export_format=export_format
        )
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to export character sheets: {str(e)}")


@router.get("/books/{book_id}/comprehensive-report")
async def get_comprehensive_character_report(
    book_id: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    session: Session = Depends(get_session),
) -> dict[str, Any]:
    """Get comprehensive character analysis report combining all metrics."""
    try:
        analysis_service = CharacterAnalysisService(session)
        
        # Get all analysis components
        usage_stats = await analysis_service.generate_character_usage_statistics(
            book_id, workspace_id, current_user.id
        )
        relationship_analysis = await analysis_service.analyze_relationship_complexity(
            book_id, workspace_id, current_user.id
        )
        arc_metrics = await analysis_service.calculate_character_arc_completeness_metrics(
            book_id, workspace_id, current_user.id
        )
        balance_recommendations = await analysis_service.generate_character_balance_recommendations(
            book_id, workspace_id, current_user.id
        )
        
        return {
            "book_id": book_id,
            "analysis_timestamp": usage_stats["analysis_timestamp"],
            "usage_statistics": usage_stats,
            "relationship_analysis": relationship_analysis,
            "arc_completeness": arc_metrics,
            "balance_analysis": balance_recommendations,
            "summary": {
                "total_characters": usage_stats["summary"]["total_characters"],
                "total_relationships": relationship_analysis["summary"]["total_relationships"],
                "arc_completion_rate": arc_metrics["summary"]["completion_percentage"],
                "balance_score": balance_recommendations["balance_score"],
            }
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate comprehensive report: {str(e)}")