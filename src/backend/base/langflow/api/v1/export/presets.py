"""
Export Presets API endpoints for Design Editor

This module provides REST API endpoints for managing export presets including
creation, retrieval, sharing, validation, and import/export functionality.
"""

from typing import Dict, List, Optional, Union
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from langflow.api.utils import CurrentActiveUser
from langflow.schema.export import ExportOptions
from langflow.services.export.export_presets_service import (
    ExportPreset,
    ExportPresetsService,
    PresetRecommendation
)

router = APIRouter(prefix="/presets", tags=["Export Presets"])


# Request/Response Models
class CreatePresetRequest(BaseModel):
    """Request model for creating a preset"""
    name: str = Field(..., description="Preset name", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Preset description", max_length=500)
    category: str = Field(default="custom", description="Preset category")
    export_options: Dict = Field(..., description="Export configuration")
    tags: List[str] = Field(default_factory=list, description="Preset tags")
    is_public: bool = Field(default=False, description="Make preset public")


class UpdatePresetRequest(BaseModel):
    """Request model for updating a preset"""
    name: Optional[str] = Field(None, description="Preset name", min_length=1, max_length=100)
    description: Optional[str] = Field(None, description="Preset description", max_length=500)
    category: Optional[str] = Field(None, description="Preset category")
    export_options: Optional[Dict] = Field(None, description="Export configuration")
    tags: Optional[List[str]] = Field(None, description="Preset tags")
    is_public: Optional[bool] = Field(None, description="Make preset public")


class SharePresetRequest(BaseModel):
    """Request model for sharing a preset"""
    user_ids: List[str] = Field(..., description="User IDs to share with")


class RecommendationRequest(BaseModel):
    """Request model for getting preset recommendations"""
    use_case: Optional[str] = Field(None, description="Use case context")
    dimensions: Optional[Dict[str, int]] = Field(None, description="Image dimensions")
    format: Optional[str] = Field(None, description="Preferred format")


class PresetExportRequest(BaseModel):
    """Request model for exporting presets"""
    preset_ids: List[str] = Field(..., description="Preset IDs to export")


class PresetImportRequest(BaseModel):
    """Request model for importing presets"""
    import_data: Dict = Field(..., description="Import data in JSON format")


class PresetResponse(BaseModel):
    """Response model for preset data"""
    id: str
    name: str
    description: Optional[str]
    category: str
    export_options: ExportOptions
    tags: List[str]
    created_by: Optional[str]
    created_at: str
    updated_at: str
    is_public: bool
    is_system: bool
    usage_count: int
    is_valid: bool
    validation_errors: List[str]


class PresetListResponse(BaseModel):
    """Response model for preset list"""
    presets: List[PresetResponse]
    total: int
    categories: List[str]


class ValidationResponse(BaseModel):
    """Response model for preset validation"""
    is_valid: bool
    errors: List[str]


class RecommendationResponse(BaseModel):
    """Response model for preset recommendations"""
    recommendations: List[PresetRecommendation]


class ExportResponse(BaseModel):
    """Response model for preset export"""
    export_data: Dict
    filename: str


class ImportResponse(BaseModel):
    """Response model for preset import"""
    imported: int
    errors: List[str]


# Global service instance
presets_service = ExportPresetsService()


@router.post("/", response_model=PresetResponse, status_code=status.HTTP_201_CREATED)
async def create_preset(
    request: CreatePresetRequest,
    current_user: CurrentActiveUser
) -> PresetResponse:
    """Create a new export preset"""
    
    try:
        preset = await presets_service.create_preset(
            preset_data=request.dict(),
            user_id=str(current_user.id) if current_user else None
        )
        
        return PresetResponse(
            id=preset.id,
            name=preset.name,
            description=preset.description,
            category=preset.category,
            export_options=preset.export_options,
            tags=preset.tags,
            created_by=preset.created_by,
            created_at=preset.created_at.isoformat(),
            updated_at=preset.updated_at.isoformat(),
            is_public=preset.is_public,
            is_system=preset.is_system,
            usage_count=preset.usage_count,
            is_valid=preset.is_valid,
            validation_errors=preset.validation_errors
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create preset: {str(e)}"
        )


@router.get("/", response_model=PresetListResponse)
async def list_presets(
    current_user: CurrentActiveUser,
    category: Optional[str] = Query(None, description="Filter by category"),
    tags: Optional[str] = Query(None, description="Filter by tags (comma-separated)"),
    include_system: bool = Query(True, description="Include system presets"),
    include_public: bool = Query(True, description="Include public presets"),
) -> PresetListResponse:
    """List available export presets with filtering options"""
    
    try:
        # Parse tags
        tag_list = None
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
        
        presets = await presets_service.list_presets(
            user_id=str(current_user.id) if current_user else None,
            category=category,
            tags=tag_list,
            include_system=include_system,
            include_public=include_public
        )
        
        # Convert to response format
        preset_responses = []
        categories = set()
        
        for preset in presets:
            preset_responses.append(PresetResponse(
                id=preset.id,
                name=preset.name,
                description=preset.description,
                category=preset.category,
                export_options=preset.export_options,
                tags=preset.tags,
                created_by=preset.created_by,
                created_at=preset.created_at.isoformat(),
                updated_at=preset.updated_at.isoformat(),
                is_public=preset.is_public,
                is_system=preset.is_system,
                usage_count=preset.usage_count,
                is_valid=preset.is_valid,
                validation_errors=preset.validation_errors
            ))
            categories.add(preset.category)
        
        return PresetListResponse(
            presets=preset_responses,
            total=len(preset_responses),
            categories=list(categories)
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list presets: {str(e)}"
        )


@router.get("/{preset_id}", response_model=PresetResponse)
async def get_preset(
    preset_id: str,
    current_user: CurrentActiveUser
) -> PresetResponse:
    """Get a specific preset by ID"""
    
    try:
        preset = await presets_service.get_preset(
            preset_id=preset_id,
            user_id=str(current_user.id) if current_user else None
        )
        
        if not preset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Preset not found"
            )
        
        return PresetResponse(
            id=preset.id,
            name=preset.name,
            description=preset.description,
            category=preset.category,
            export_options=preset.export_options,
            tags=preset.tags,
            created_by=preset.created_by,
            created_at=preset.created_at.isoformat(),
            updated_at=preset.updated_at.isoformat(),
            is_public=preset.is_public,
            is_system=preset.is_system,
            usage_count=preset.usage_count,
            is_valid=preset.is_valid,
            validation_errors=preset.validation_errors
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get preset: {str(e)}"
        )


@router.put("/{preset_id}", response_model=PresetResponse)
async def update_preset(
    preset_id: str,
    request: UpdatePresetRequest,
    current_user: CurrentActiveUser
) -> PresetResponse:
    """Update an existing preset"""
    
    try:
        # Filter out None values
        updates = {k: v for k, v in request.dict().items() if v is not None}
        
        preset = await presets_service.update_preset(
            preset_id=preset_id,
            updates=updates,
            user_id=str(current_user.id) if current_user else None
        )
        
        if not preset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Preset not found"
            )
        
        return PresetResponse(
            id=preset.id,
            name=preset.name,
            description=preset.description,
            category=preset.category,
            export_options=preset.export_options,
            tags=preset.tags,
            created_by=preset.created_by,
            created_at=preset.created_at.isoformat(),
            updated_at=preset.updated_at.isoformat(),
            is_public=preset.is_public,
            is_system=preset.is_system,
            usage_count=preset.usage_count,
            is_valid=preset.is_valid,
            validation_errors=preset.validation_errors
        )
        
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update preset: {str(e)}"
        )


@router.delete("/{preset_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_preset(
    preset_id: str,
    current_user: CurrentActiveUser
):
    """Delete a preset"""
    
    try:
        success = await presets_service.delete_preset(
            preset_id=preset_id,
            user_id=str(current_user.id) if current_user else None
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Preset not found"
            )
        
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete preset: {str(e)}"
        )


@router.post("/{preset_id}/use", response_model=ExportOptions)
async def use_preset(
    preset_id: str,
    current_user: CurrentActiveUser
) -> ExportOptions:
    """Use a preset and get its export options"""
    
    try:
        export_options = await presets_service.use_preset(
            preset_id=preset_id,
            user_id=str(current_user.id) if current_user else None
        )
        
        if not export_options:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Preset not found"
            )
        
        return export_options
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to use preset: {str(e)}"
        )


@router.post("/{preset_id}/share", status_code=status.HTTP_200_OK)
async def share_preset(
    preset_id: str,
    request: SharePresetRequest,
    current_user: CurrentActiveUser
):
    """Share a preset with other users"""
    
    try:
        success = await presets_service.share_preset(
            preset_id=preset_id,
            target_user_ids=request.user_ids,
            user_id=str(current_user.id) if current_user else None
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Preset not found"
            )
        
        return {"message": "Preset shared successfully"}
        
    except PermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to share preset: {str(e)}"
        )


@router.post("/{preset_id}/validate", response_model=ValidationResponse)
async def validate_preset(
    preset_id: str,
    current_user: CurrentActiveUser
) -> ValidationResponse:
    """Validate a preset configuration"""
    
    try:
        preset = await presets_service.get_preset(
            preset_id=preset_id,
            user_id=str(current_user.id) if current_user else None
        )
        
        if not preset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Preset not found"
            )
        
        validation_result = await presets_service.validate_preset(preset)
        
        return ValidationResponse(
            is_valid=validation_result["is_valid"],
            errors=validation_result["errors"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to validate preset: {str(e)}"
        )


@router.post("/recommendations", response_model=RecommendationResponse)
async def get_recommendations(
    request: RecommendationRequest,
    current_user: CurrentActiveUser
) -> RecommendationResponse:
    """Get preset recommendations based on context"""
    
    try:
        recommendations = await presets_service.get_recommendations(
            context=request.dict(exclude_none=True),
            user_id=str(current_user.id) if current_user else None
        )
        
        return RecommendationResponse(recommendations=recommendations)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get recommendations: {str(e)}"
        )


@router.post("/export", response_model=ExportResponse)
async def export_presets(
    request: PresetExportRequest,
    current_user: CurrentActiveUser
) -> ExportResponse:
    """Export presets to JSON format"""
    
    try:
        export_data = await presets_service.export_presets(
            preset_ids=request.preset_ids,
            user_id=str(current_user.id) if current_user else None
        )
        
        filename = f"export_presets_{len(request.preset_ids)}_items.json"
        
        return ExportResponse(
            export_data=export_data,
            filename=filename
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export presets: {str(e)}"
        )


@router.post("/import", response_model=ImportResponse)
async def import_presets(
    request: PresetImportRequest,
    current_user: CurrentActiveUser
) -> ImportResponse:
    """Import presets from JSON data"""
    
    try:
        import_result = await presets_service.import_presets(
            import_data=request.import_data,
            user_id=str(current_user.id) if current_user else None
        )
        
        return ImportResponse(
            imported=import_result["imported"],
            errors=import_result["errors"]
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to import presets: {str(e)}"
        )


@router.get("/categories/list", response_model=List[str])
async def list_categories(
    current_user: CurrentActiveUser
) -> List[str]:
    """Get list of available preset categories"""
    
    try:
        presets = await presets_service.list_presets(
            user_id=str(current_user.id) if current_user else None
        )
        
        categories = set(preset.category for preset in presets)
        return sorted(list(categories))
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get categories: {str(e)}"
        )


@router.get("/health", status_code=status.HTTP_200_OK)
async def health_check():
    """Health check for export presets service"""
    
    try:
        # Test basic functionality
        test_presets = await presets_service.list_presets(include_system=True)
        
        return {
            "status": "healthy",
            "message": "Export presets service is operational",
            "system_presets": len([p for p in test_presets if p.is_system]),
            "total_presets": len(test_presets)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Service health check failed: {str(e)}"
        )