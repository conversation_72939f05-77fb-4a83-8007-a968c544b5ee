"""
Social Media Publishing API Endpoints

Provides comprehensive social media publishing capabilities with platform integrations,
content optimization, and publishing workflow management.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, File, UploadFile
from fastapi.responses import StreamingResponse
from io import BytesIO
import json

from langflow.api.utils import CurrentActiveU<PERSON>, verify_workspace_access
from langflow.schema.export import (
    SocialMediaPost, SocialMediaCredentials, PublishResult, PlatformOptimization,
    SocialMediaAccount, SocialMediaAnalytics, ContentModerationResult, PlatformLimits
)
from langflow.services.export.social_media_service import SocialMediaService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/social-media", tags=["Social Media Publishing"])

# Initialize social media service
social_media_service = SocialMediaService()


@router.post("/publish/", response_model=PublishResult)
async def publish_design(
    platform: str,
    post_data: SocialMediaPost,
    credentials: SocialMediaCredentials,
    workspace_id: str,
    current_user: CurrentActiveUser,
    design_file: UploadFile = File(...),
) -> PublishResult:
    """
    Publish design to social media platform.
    
    Uploads and publishes a design to the specified social media platform
    with platform-specific optimizations and content validation.
    """
    
    try:
        # Verify workspace access
        await verify_workspace_access(workspace_id, current_user.id)
        
        # Read uploaded file
        image_data = await design_file.read()
        
        if not image_data:
            raise HTTPException(status_code=400, detail="No image data provided")
        
        # Validate platform
        if platform not in social_media_service.publishers:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported platform: {platform}. Supported platforms: {list(social_media_service.publishers.keys())}"
            )
        
        # Publish design
        result = await social_media_service.publish_design(
            image_data=image_data,
            platform=platform,
            post_data=post_data,
            credentials=credentials,
            workspace_id=workspace_id
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Publishing failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Publishing failed: {str(e)}")


@router.post("/publish/batch/", response_model=List[PublishResult])
async def publish_batch(
    platforms: List[str],
    post_data: SocialMediaPost,
    credentials_list: List[SocialMediaCredentials],
    workspace_id: str,
    background_tasks: BackgroundTasks,
    current_user: CurrentActiveUser,
    design_file: UploadFile = File(...),
) -> List[PublishResult]:
    """
    Publish design to multiple social media platforms simultaneously.
    
    Optimizes and publishes the same design to multiple platforms
    with platform-specific optimizations.
    """
    
    try:
        # Verify workspace access
        await verify_workspace_access(workspace_id, current_user.id)
        
        # Read uploaded file
        image_data = await design_file.read()
        
        if not image_data:
            raise HTTPException(status_code=400, detail="No image data provided")
        
        # Validate platforms and credentials
        if len(platforms) != len(credentials_list):
            raise HTTPException(
                status_code=400,
                detail="Number of platforms must match number of credential sets"
            )
        
        results = []
        
        # Publish to each platform
        for platform, credentials in zip(platforms, credentials_list):
            if platform not in social_media_service.publishers:
                results.append(PublishResult(
                    platform=platform,
                    status="failed",
                    error=f"Unsupported platform: {platform}"
                ))
                continue
            
            try:
                result = await social_media_service.publish_design(
                    image_data=image_data,
                    platform=platform,
                    post_data=post_data,
                    credentials=credentials,
                    workspace_id=workspace_id
                )
                results.append(result)
                
            except Exception as e:
                logger.error(f"Publishing to {platform} failed: {str(e)}")
                results.append(PublishResult(
                    platform=platform,
                    status="failed",
                    error=str(e)
                ))
        
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Batch publishing failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Batch publishing failed: {str(e)}")


@router.post("/optimize/", response_model=Dict[str, Any])
async def optimize_for_platform(
    platform: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    design_file: UploadFile = File(...),
) -> Dict[str, Any]:
    """
    Optimize design for specific social media platform.
    
    Returns optimized image data and metadata about the optimization process.
    """
    
    try:
        # Verify workspace access
        await verify_workspace_access(workspace_id, current_user.id)
        
        # Read uploaded file
        image_data = await design_file.read()
        
        if not image_data:
            raise HTTPException(status_code=400, detail="No image data provided")
        
        # Validate platform
        if platform not in social_media_service.platform_specs:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported platform: {platform}"
            )
        
        # Optimize image
        original_size = len(image_data)
        optimized_data = await social_media_service.optimize_for_platform(image_data, platform)
        optimized_size = len(optimized_data)
        
        # Get platform specifications
        spec = social_media_service.platform_specs[platform]
        
        return {
            "platform": platform,
            "original_size_bytes": original_size,
            "optimized_size_bytes": optimized_size,
            "compression_ratio": (original_size - optimized_size) / original_size if original_size > 0 else 0,
            "platform_specs": spec,
            "optimization_applied": optimized_size != original_size,
            "download_url": f"/api/v1/export/social-media/download-optimized/{platform}"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Optimization failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Optimization failed: {str(e)}")


@router.post("/optimize/download/", response_class=StreamingResponse)
async def download_optimized(
    platform: str,
    workspace_id: str,
    current_user: CurrentActiveUser,
    design_file: UploadFile = File(...),
) -> StreamingResponse:
    """
    Download optimized design for specific platform.
    
    Returns the optimized image file ready for platform upload.
    """
    
    try:
        # Verify workspace access
        await verify_workspace_access(workspace_id, current_user.id)
        
        # Read uploaded file
        image_data = await design_file.read()
        
        if not image_data:
            raise HTTPException(status_code=400, detail="No image data provided")
        
        # Optimize image
        optimized_data = await social_media_service.optimize_for_platform(image_data, platform)
        
        # Determine file extension based on platform
        platform_spec = social_media_service.platform_specs.get(platform, {})
        formats = platform_spec.get("image_formats", ["jpg"])
        primary_format = formats[0] if formats else "jpg"
        
        # Create response
        output = BytesIO(optimized_data)
        
        return StreamingResponse(
            output,
            media_type=f"image/{primary_format}",
            headers={
                "Content-Disposition": f"attachment; filename=design_optimized_{platform}.{primary_format}"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Download failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Download failed: {str(e)}")


@router.post("/validate/", response_model=Dict[str, Any])
async def validate_content(
    platform: str,
    post_data: SocialMediaPost,
    workspace_id: str,
    current_user: CurrentActiveUser,
    design_file: UploadFile = File(...),
) -> Dict[str, Any]:
    """
    Validate content for social media platform.
    
    Checks content against platform policies and requirements,
    returns validation results and recommendations.
    """
    
    try:
        # Verify workspace access
        await verify_workspace_access(workspace_id, current_user.id)
        
        # Read uploaded file
        image_data = await design_file.read()
        
        if not image_data:
            raise HTTPException(status_code=400, detail="No image data provided")
        
        # Validate content
        validation_result = await social_media_service.validate_content(
            image_data=image_data,
            post_data=post_data,
            platform=platform
        )
        
        return validation_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Validation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Validation failed: {str(e)}")


@router.get("/platforms/", response_model=List[str])
async def get_supported_platforms() -> List[str]:
    """
    Get list of supported social media platforms.
    
    Returns a list of all platforms that support publishing integration.
    """
    
    return list(social_media_service.publishers.keys())


@router.get("/platform/{platform}/specs/", response_model=Dict[str, Any])
async def get_platform_specs(platform: str) -> Dict[str, Any]:
    """
    Get detailed specifications for a social media platform.
    
    Returns platform requirements, limitations, and optimization settings.
    """
    
    if platform not in social_media_service.platform_specs:
        raise HTTPException(status_code=404, detail=f"Platform not found: {platform}")
    
    return social_media_service.platform_specs[platform]


@router.get("/platform/{platform}/insights/", response_model=Dict[str, Any])
async def get_platform_insights(platform: str) -> Dict[str, Any]:
    """
    Get insights and recommendations for a platform.
    
    Returns platform-specific insights, best practices, and recommendations.
    """
    
    try:
        insights = await social_media_service.get_platform_insights(platform)
        return insights
        
    except Exception as e:
        logger.error(f"Failed to get insights for {platform}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get insights: {str(e)}")


@router.post("/credentials/validate/", response_model=Dict[str, Any])
async def validate_credentials(
    credentials: SocialMediaCredentials,
    current_user: CurrentActiveUser,
) -> Dict[str, Any]:
    """
    Validate social media platform credentials.
    
    Checks if provided credentials are valid and have required permissions.
    """
    
    try:
        platform = credentials.platform
        
        if platform not in social_media_service.publishers:
            raise HTTPException(status_code=400, detail=f"Unsupported platform: {platform}")
        
        publisher = social_media_service.publishers[platform]
        is_valid = await publisher.validate_credentials(credentials)
        
        return {
            "platform": platform,
            "valid": is_valid,
            "scopes": credentials.scopes,
            "account_type": credentials.account_type,
            "token_expires_at": credentials.token_expires_at,
            "validated_at": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Credential validation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Credential validation failed: {str(e)}")


@router.get("/presets/", response_model=List[Dict[str, Any]])
async def get_optimization_presets() -> List[Dict[str, Any]]:
    """
    Get optimization presets for different platforms and use cases.
    
    Returns predefined optimization settings for common scenarios.
    """
    
    presets = [
        {
            "id": "instagram_feed",
            "name": "Instagram Feed Post",
            "description": "Optimized for Instagram feed posts with square aspect ratio",
            "platform": "instagram",
            "settings": {
                "aspect_ratio": "1:1",
                "max_size": 1080,
                "quality": 85,
                "format": "jpg"
            }
        },
        {
            "id": "instagram_story",
            "name": "Instagram Story",
            "description": "Optimized for Instagram stories with vertical aspect ratio",
            "platform": "instagram",
            "settings": {
                "aspect_ratio": "9:16",
                "max_size": 1080,
                "quality": 85,
                "format": "jpg"
            }
        },
        {
            "id": "facebook_post",
            "name": "Facebook Post",
            "description": "Optimized for Facebook timeline posts",
            "platform": "facebook",
            "settings": {
                "aspect_ratio": "1.91:1",
                "max_size": 1200,
                "quality": 90,
                "format": "jpg"
            }
        },
        {
            "id": "twitter_post",
            "name": "Twitter Post",
            "description": "Optimized for Twitter timeline with high quality",
            "platform": "twitter",
            "settings": {
                "aspect_ratio": "16:9",
                "max_size": 1024,
                "quality": 95,
                "format": "png"
            }
        },
        {
            "id": "linkedin_post",
            "name": "LinkedIn Post",
            "description": "Optimized for LinkedIn professional content",
            "platform": "linkedin",
            "settings": {
                "aspect_ratio": "1.91:1",
                "max_size": 1200,
                "quality": 90,
                "format": "png"
            }
        },
        {
            "id": "pinterest_pin",
            "name": "Pinterest Pin",
            "description": "Optimized for Pinterest with vertical aspect ratio",
            "platform": "pinterest",
            "settings": {
                "aspect_ratio": "2:3",
                "max_size": 1000,
                "quality": 90,
                "format": "jpg"
            }
        }
    ]
    
    return presets


@router.get("/health/", response_model=Dict[str, Any])
async def get_service_health() -> Dict[str, Any]:
    """
    Get health status of social media publishing service.
    
    Returns service status, platform availability, and performance metrics.
    """
    
    health_status = {
        "service": "healthy",
        "timestamp": datetime.now().isoformat(),
        "platforms": {},
        "features": {
            "image_optimization": "available",
            "batch_publishing": "available",
            "content_validation": "available",
            "credential_validation": "available"
        }
    }
    
    # Check platform availability
    for platform in social_media_service.publishers.keys():
        health_status["platforms"][platform] = {
            "status": "available",
            "publisher_loaded": True,
            "specs_available": platform in social_media_service.platform_specs
        }
    
    return health_status


@router.post("/schedule/", response_model=PublishResult)
async def schedule_post(
    platform: str,
    post_data: SocialMediaPost,
    credentials: SocialMediaCredentials,
    workspace_id: str,
    current_user: CurrentActiveUser,
    design_file: UploadFile = File(...),
) -> PublishResult:
    """
    Schedule a post for future publishing.
    
    Uploads and schedules a design to be published at a specified time.
    Note: Actual scheduling depends on platform support.
    """
    
    try:
        # Verify workspace access
        await verify_workspace_access(workspace_id, current_user.id)
        
        # Check if scheduling is requested
        if not post_data.schedule_time:
            raise HTTPException(
                status_code=400,
                detail="Schedule time is required for scheduled posts"
            )
        
        # For now, return a mock scheduled result
        # In a real implementation, this would integrate with platform scheduling APIs
        # or a job queue system
        
        return PublishResult(
            platform=platform,
            status="scheduled",
            scheduled_for=post_data.schedule_time,
            post_id=f"scheduled_{platform}_{datetime.now().timestamp()}",
            optimization_applied=True
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Scheduling failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Scheduling failed: {str(e)}")


@router.get("/analytics/{platform}/{post_id}/", response_model=SocialMediaAnalytics)
async def get_post_analytics(
    platform: str,
    post_id: str,
    credentials: SocialMediaCredentials,
    current_user: CurrentActiveUser,
) -> SocialMediaAnalytics:
    """
    Get analytics for a published post.
    
    Retrieves engagement metrics and performance data from the platform.
    Note: Requires appropriate platform permissions.
    """
    
    try:
        # This would integrate with platform analytics APIs
        # For now, return mock analytics data
        
        mock_analytics = SocialMediaAnalytics(
            post_id=post_id,
            platform=platform,
            likes=42,
            comments=8,
            shares=12,
            impressions=1250,
            reach=980,
            engagement_rate=5.2,
            collected_at=datetime.now(),
            post_age_hours=24.5,
            platform_metrics={
                "saves": 15 if platform == "instagram" else 0,
                "clicks": 28 if platform == "twitter" else 0
            }
        )
        
        return mock_analytics
        
    except Exception as e:
        logger.error(f"Analytics retrieval failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analytics retrieval failed: {str(e)}")