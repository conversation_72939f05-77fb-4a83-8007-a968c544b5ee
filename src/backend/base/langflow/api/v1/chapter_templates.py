from typing import List, Optional
from uuid import uuid4

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session, select, and_, or_

from langflow.api.utils import DbSession
from langflow.services.auth.utils import get_current_active_user
from langflow.services.database.models.user import User
from langflow.services.database.models.workspace.model import Workspace
from langflow.services.database.models.chapter_template.model import ChapterTemplate, ChapterTemplateCreate, ChapterTemplateUpdate
from langflow.services.deps import verify_workspace_access

router = APIRouter(prefix="/chapter-templates", tags=["Chapter Templates"])


@router.post("", response_model=ChapterTemplate)
async def create_chapter_template(
    template_data: ChapterTemplateCreate,
    workspace_id: str = Query(..., description="Workspace ID"),
    db: Session = Depends(DbSession),
    current_user: User = Depends(get_current_active_user),
):
    """Create a new chapter template."""
    await verify_workspace_access(workspace_id, current_user.id, db)
    
    template = ChapterTemplate(
        id=str(uuid4()),
        **template_data.dict(),
        created_by=current_user.id,
        workspace_id=workspace_id
    )
    
    db.add(template)
    db.commit()
    db.refresh(template)
    
    return template


@router.get("", response_model=List[ChapterTemplate])
async def list_chapter_templates(
    workspace_id: str = Query(..., description="Workspace ID"),
    genre: Optional[str] = Query(None, description="Filter by genre"),
    is_public: Optional[bool] = Query(None, description="Filter by public status"),
    created_by_me: Optional[bool] = Query(None, description="Show only templates created by current user"),
    db: Session = Depends(DbSession),
    current_user: User = Depends(get_current_active_user),
):
    """List chapter templates with optional filters."""
    await verify_workspace_access(workspace_id, current_user.id, db)
    
    query = select(ChapterTemplate).where(
        or_(
            ChapterTemplate.workspace_id == workspace_id,
            ChapterTemplate.is_public == True
        )
    )
    
    if genre:
        query = query.where(ChapterTemplate.genre == genre)
    
    if is_public is not None:
        query = query.where(ChapterTemplate.is_public == is_public)
        
    if created_by_me:
        query = query.where(ChapterTemplate.created_by == current_user.id)
    
    templates = db.exec(query).all()
    return templates


@router.get("/{template_id}", response_model=ChapterTemplate)
async def get_chapter_template(
    template_id: str,
    workspace_id: str = Query(..., description="Workspace ID"),
    db: Session = Depends(DbSession),
    current_user: User = Depends(get_current_active_user),
):
    """Get a specific chapter template."""
    await verify_workspace_access(workspace_id, current_user.id, db)
    
    template = db.get(ChapterTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Chapter template not found")
    
    # Check if user has access to this template
    if template.workspace_id != workspace_id and not template.is_public:
        raise HTTPException(status_code=403, detail="Access denied to this template")
    
    return template


@router.put("/{template_id}", response_model=ChapterTemplate)
async def update_chapter_template(
    template_id: str,
    template_data: ChapterTemplateUpdate,
    workspace_id: str = Query(..., description="Workspace ID"),
    db: Session = Depends(DbSession),
    current_user: User = Depends(get_current_active_user),
):
    """Update a chapter template."""
    await verify_workspace_access(workspace_id, current_user.id, db)
    
    template = db.get(ChapterTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Chapter template not found")
    
    # Check if user owns this template or has permission to edit
    if template.created_by != current_user.id and template.workspace_id != workspace_id:
        raise HTTPException(status_code=403, detail="Access denied to edit this template")
    
    # Update template fields
    update_data = template_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(template, field, value)
    
    db.add(template)
    db.commit()
    db.refresh(template)
    
    return template


@router.delete("/{template_id}")
async def delete_chapter_template(
    template_id: str,
    workspace_id: str = Query(..., description="Workspace ID"),
    db: Session = Depends(DbSession),
    current_user: User = Depends(get_current_active_user),
):
    """Delete a chapter template."""
    await verify_workspace_access(workspace_id, current_user.id, db)
    
    template = db.get(ChapterTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Chapter template not found")
    
    # Check if user owns this template
    if template.created_by != current_user.id:
        raise HTTPException(status_code=403, detail="Only template creator can delete it")
    
    db.delete(template)
    db.commit()
    
    return {"message": "Chapter template deleted successfully"}


@router.post("/{template_id}/duplicate", response_model=ChapterTemplate)
async def duplicate_chapter_template(
    template_id: str,
    workspace_id: str = Query(..., description="Workspace ID"),
    new_name: Optional[str] = Query(None, description="Name for the duplicated template"),
    db: Session = Depends(DbSession),
    current_user: User = Depends(get_current_active_user),
):
    """Duplicate a chapter template."""
    await verify_workspace_access(workspace_id, current_user.id, db)
    
    original_template = db.get(ChapterTemplate, template_id)
    if not original_template:
        raise HTTPException(status_code=404, detail="Chapter template not found")
    
    # Check access to original template
    if original_template.workspace_id != workspace_id and not original_template.is_public:
        raise HTTPException(status_code=403, detail="Access denied to this template")
    
    # Create duplicate
    duplicate_name = new_name or f"{original_template.name} (Copy)"
    
    duplicate_template = ChapterTemplate(
        id=str(uuid4()),
        name=duplicate_name,
        genre=original_template.genre,
        description=original_template.description,
        default_scenes=original_template.default_scenes.copy(),
        scene_templates=original_template.scene_templates.copy(),
        word_count_target=original_template.word_count_target,
        recommended_scene_count=original_template.recommended_scene_count,
        pacing_guidelines=original_template.pacing_guidelines,
        structure_notes=original_template.structure_notes,
        is_public=False,  # Duplicates are private by default
        created_by=current_user.id,
        workspace_id=workspace_id,
        usage_count=0
    )
    
    db.add(duplicate_template)
    db.commit()
    db.refresh(duplicate_template)
    
    return duplicate_template


@router.get("/{template_id}/usage-stats")
async def get_template_usage_stats(
    template_id: str,
    workspace_id: str = Query(..., description="Workspace ID"),
    db: Session = Depends(DbSession),
    current_user: User = Depends(get_current_active_user),
):
    """Get usage statistics for a template."""
    await verify_workspace_access(workspace_id, current_user.id, db)
    
    template = db.get(ChapterTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Chapter template not found")
    
    # Check access
    if template.workspace_id != workspace_id and not template.is_public:
        raise HTTPException(status_code=403, detail="Access denied to this template")
    
    # Count chapters using this template
    from langflow.services.database.models.book.model import Chapter
    chapters_using_template = db.exec(
        select(Chapter).where(Chapter.template_id == template_id)
    ).all()
    
    return {
        "template_id": template_id,
        "template_name": template.name,
        "usage_count": template.usage_count,
        "chapters_created": len(chapters_using_template),
        "last_used": max(
            (chapter.created_at for chapter in chapters_using_template),
            default=None
        )
    }


@router.get("/genres/list")
async def list_available_genres(
    workspace_id: str = Query(..., description="Workspace ID"),
    db: Session = Depends(DbSession),
    current_user: User = Depends(get_current_active_user),
):
    """Get list of available genres from existing templates."""
    await verify_workspace_access(workspace_id, current_user.id, db)
    
    # Get unique genres from templates accessible to user
    genres_query = select(ChapterTemplate.genre).distinct().where(
        or_(
            ChapterTemplate.workspace_id == workspace_id,
            ChapterTemplate.is_public == True
        )
    )
    
    genres = db.exec(genres_query).all()
    
    # Include standard genres even if not in use
    standard_genres = [
        'Fantasy', 'Mystery', 'Romance', 'Thriller', 'Horror', 'Science Fiction',
        'Historical Fiction', 'Literary Fiction', 'Adventure', 'Drama'
    ]
    
    all_genres = sorted(list(set(genres + standard_genres)))
    
    return {"genres": all_genres}


@router.post("/{template_id}/increment-usage")
async def increment_template_usage(
    template_id: str,
    workspace_id: str = Query(..., description="Workspace ID"),
    db: Session = Depends(DbSession),
    current_user: User = Depends(get_current_active_user),
):
    """Increment usage count when template is applied."""
    await verify_workspace_access(workspace_id, current_user.id, db)
    
    template = db.get(ChapterTemplate, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Chapter template not found")
    
    template.usage_count += 1
    db.add(template)
    db.commit()
    
    return {"message": "Usage count incremented", "new_count": template.usage_count}