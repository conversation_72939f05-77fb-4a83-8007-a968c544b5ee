"""API response utilities."""

from typing import Dict, Any


def build_status_response(message: str, success: bool = True, **kwargs) -> Dict[str, Any]:
    """Build a standardized status response.
    
    Args:
        message: Status message
        success: Whether the operation was successful
        **kwargs: Additional data to include in response
        
    Returns:
        Dictionary with status response
    """
    response = {
        "success": success,
        "message": message
    }
    
    # Add any additional data
    if kwargs:
        response.update(kwargs)
    
    return response


def build_error_response(message: str, error_code: str = None, **kwargs) -> Dict[str, Any]:
    """Build a standardized error response.
    
    Args:
        message: Error message
        error_code: Optional error code
        **kwargs: Additional error data
        
    Returns:
        Dictionary with error response
    """
    response = {
        "success": False,
        "error": message
    }
    
    if error_code:
        response["error_code"] = error_code
    
    # Add any additional error data
    if kwargs:
        response.update(kwargs)
    
    return response


def build_data_response(data: Any, message: str = None, **kwargs) -> Dict[str, Any]:
    """Build a standardized data response.
    
    Args:
        data: The data to return
        message: Optional success message
        **kwargs: Additional metadata
        
    Returns:
        Dictionary with data response
    """
    response = {
        "success": True,
        "data": data
    }
    
    if message:
        response["message"] = message
    
    # Add any additional metadata
    if kwargs:
        response.update(kwargs)
    
    return response
