"""Amazon KDP Publishing Service for direct publishing integration."""

import os
import asyncio
from datetime import datetime
from typing import Optional, Dict, Any, List
import aiohttp
import json
import logging
from pathlib import Path

from sqlmodel import select
from fastapi import HTTPException

from langflow.services.base import Service
from langflow.services.deps import get_session, verify_workspace_access, get_current_user

logger = logging.getLogger(__name__)


class AmazonKDPService(Service):
    """Service for Amazon KDP API integration and publishing workflow."""
    
    name = "amazon_kdp_service"
    
    def __init__(self):
        """Initialize Amazon KDP service with API credentials."""
        super().__init__()
        self.kdp_api_key = os.getenv("AMAZON_KDP_API_KEY")
        self.kdp_secret = os.getenv("AMAZON_KDP_SECRET")  
        self.kdp_base_url = os.getenv("AMAZON_KDP_BASE_URL", "https://kdp.amazon.com/api/v1")
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Validate required credentials
        if not self.kdp_api_key or not self.kdp_secret:
            logger.warning("Amazon KDP credentials not configured. KDP features will be disabled.")
            self.enabled = False
        else:
            self.enabled = True
            
    async def initialize(self) -> None:
        """Initialize the KDP service and HTTP session."""
        if self.enabled:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=300),  # 5 minute timeout
                headers={
                    "User-Agent": "Langflow-KDP-Integration/1.0",
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
            )
            logger.info("Amazon KDP Service initialized successfully")
        else:
            logger.info("Amazon KDP Service disabled - credentials not configured")
    
    async def teardown(self) -> None:
        """Clean up HTTP session."""
        if self.session and not self.session.closed:
            await self.session.close()
            
    async def get_kdp_headers(self) -> Dict[str, str]:
        """Generate authentication headers for KDP API requests."""
        if not self.enabled:
            raise HTTPException(status_code=503, detail="KDP service not configured")
            
        # Generate timestamp and signature for KDP API authentication
        timestamp = datetime.utcnow().isoformat() + "Z"
        
        # In a real implementation, this would use proper AWS signature v4
        # For now, using basic auth structure
        headers = {
            "Authorization": f"AWS4-HMAC-SHA256 Credential={self.kdp_api_key}",
            "X-Amz-Date": timestamp,
            "Content-Type": "application/json"
        }
        
        return headers
    
    async def make_kdp_request(self, method: str, endpoint: str, 
                              data: Optional[Dict] = None, 
                              headers: Optional[Dict] = None) -> aiohttp.ClientResponse:
        """Make authenticated request to KDP API with retry logic."""
        if not self.session:
            raise HTTPException(status_code=503, detail="KDP service not initialized")
            
        url = f"{self.kdp_base_url}{endpoint}"
        request_headers = headers or await self.get_kdp_headers()
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                async with self.session.request(
                    method, url, 
                    json=data, 
                    headers=request_headers
                ) as response:
                    
                    # Handle rate limiting
                    if response.status == 429:
                        wait_time = int(response.headers.get("Retry-After", 2 ** attempt))
                        logger.warning(f"KDP API rate limited. Waiting {wait_time}s before retry {attempt + 1}")
                        await asyncio.sleep(wait_time)
                        continue
                        
                    # Handle server errors with retry
                    if response.status >= 500 and attempt < max_retries - 1:
                        logger.warning(f"KDP API server error {response.status}. Retry {attempt + 1}")
                        await asyncio.sleep(2 ** attempt)
                        continue
                        
                    return response
                    
            except aiohttp.ClientError as e:
                if attempt < max_retries - 1:
                    logger.warning(f"KDP API connection error: {e}. Retry {attempt + 1}")
                    await asyncio.sleep(2 ** attempt)
                    continue
                raise HTTPException(status_code=503, detail=f"KDP API connection failed: {e}")
                
        raise HTTPException(status_code=503, detail="KDP API request failed after retries")
    
    async def make_kdp_file_request(self, method: str, endpoint: str,
                                   data: Dict, files: Dict,
                                   headers: Optional[Dict] = None) -> aiohttp.ClientResponse:
        """Make file upload request to KDP API."""
        if not self.session:
            raise HTTPException(status_code=503, detail="KDP service not initialized")
            
        url = f"{self.kdp_base_url}{endpoint}"
        request_headers = headers or await self.get_kdp_headers()
        
        # Remove Content-Type for file uploads (let aiohttp set it)
        if "Content-Type" in request_headers:
            del request_headers["Content-Type"]
        
        form_data = aiohttp.FormData()
        
        # Add form fields
        for key, value in data.items():
            form_data.add_field(key, str(value))
            
        # Add files
        for field_name, file_obj in files.items():
            form_data.add_field(field_name, file_obj)
        
        try:
            async with self.session.request(
                method, url,
                data=form_data,
                headers=request_headers
            ) as response:
                return response
                
        except aiohttp.ClientError as e:
            raise HTTPException(status_code=503, detail=f"KDP file upload failed: {e}")
    
    async def validate_kdp_credentials(self) -> Dict[str, Any]:
        """Validate KDP API credentials by making a test request."""
        if not self.enabled:
            return {"valid": False, "error": "KDP credentials not configured"}
            
        try:
            response = await self.make_kdp_request("GET", "/account/info")
            
            if response.status == 200:
                account_data = await response.json()
                return {
                    "valid": True,
                    "account_id": account_data.get("account_id"),
                    "marketplace": account_data.get("default_marketplace", "US")
                }
            elif response.status == 401:
                return {"valid": False, "error": "Invalid KDP credentials"}
            else:
                return {"valid": False, "error": f"KDP API error: {response.status}"}
                
        except Exception as e:
            logger.error(f"KDP credential validation failed: {e}")
            return {"valid": False, "error": str(e)}
    
    async def create_kdp_project(self, publishing_project: "PublishingProject",
                               metadata: "BookMetadata", workspace_id: str) -> Dict[str, Any]:
        """Create new project in Amazon KDP."""
        await verify_workspace_access(workspace_id, get_current_user().id)
        
        if not self.enabled:
            raise HTTPException(status_code=503, detail="KDP service not configured")
        
        # Prepare KDP project data according to KDP API schema
        project_data = {
            "title": metadata.title,
            "subtitle": metadata.subtitle,
            "authors": [metadata.primary_author] + (metadata.co_authors or []),
            "description": metadata.description,
            "categories": [cat for cat in [metadata.primary_category, metadata.secondary_category] if cat],
            "keywords": metadata.keywords or [],
            "language": metadata.language,
            "publication_date": publishing_project.publication_date.isoformat() if publishing_project.publication_date else None,
            "formats": {
                "print": publishing_project.print_enabled,
                "ebook": publishing_project.ebook_enabled,
                "hardcover": publishing_project.hardcover_enabled
            }
        }
        
        try:
            response = await self.make_kdp_request("POST", "/projects", project_data)
            
            if response.status == 201:
                kdp_data = await response.json()
                
                # Update publishing project with KDP project ID
                publishing_project.kdp_project_id = kdp_data["project_id"]
                publishing_project.status = "created"
                publishing_project.last_sync = datetime.utcnow()
                
                # Save the updated project (this would use the database service)
                # await self.update_publishing_project(publishing_project.id, publishing_project)
                
                logger.info(f"Created KDP project {kdp_data['project_id']} for publishing project {publishing_project.id}")
                
                return {
                    "success": True,
                    "kdp_project_id": kdp_data["project_id"],
                    "status": "created",
                    "estimated_setup_time": kdp_data.get("estimated_setup_time", "24-48 hours")
                }
            elif response.status == 400:
                error_data = await response.json()
                raise HTTPException(status_code=400, detail=f"Invalid project data: {error_data}")
            elif response.status == 409:
                raise HTTPException(status_code=409, detail="Project with this title already exists in KDP")
            else:
                error_text = await response.text()
                raise HTTPException(status_code=response.status, detail=f"KDP API error: {error_text}")
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to create KDP project: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to create KDP project: {e}")
    
    async def get_project_status(self, kdp_project_id: str, workspace_id: str) -> Dict[str, Any]:
        """Get current status of KDP project."""
        await verify_workspace_access(workspace_id, get_current_user().id)
        
        try:
            response = await self.make_kdp_request("GET", f"/projects/{kdp_project_id}/status")
            
            if response.status == 200:
                status_data = await response.json()
                return {
                    "success": True,
                    "project_id": kdp_project_id,
                    "status": status_data["status"],
                    "stage": status_data.get("stage", "unknown"),
                    "manuscript_status": status_data.get("manuscript_status"),
                    "cover_status": status_data.get("cover_status"),
                    "metadata_status": status_data.get("metadata_status"),
                    "publishing_status": status_data.get("publishing_status"),
                    "last_updated": status_data.get("last_updated")
                }
            elif response.status == 404:
                raise HTTPException(status_code=404, detail="KDP project not found")
            else:
                error_text = await response.text()
                raise HTTPException(status_code=response.status, detail=f"Failed to get project status: {error_text}")
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to get KDP project status: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to get project status: {e}")
    
    async def update_project_metadata(self, kdp_project_id: str, metadata: "BookMetadata",
                                    workspace_id: str) -> Dict[str, Any]:
        """Update project metadata in KDP."""
        await verify_workspace_access(workspace_id, get_current_user().id)
        
        metadata_update = {
            "title": metadata.title,
            "subtitle": metadata.subtitle,
            "description": metadata.description,
            "categories": [cat for cat in [metadata.primary_category, metadata.secondary_category] if cat],
            "keywords": metadata.keywords or [],
            "authors": [metadata.primary_author] + (metadata.co_authors or []),
            "copyright_year": metadata.copyright_year,
            "copyright_holder": metadata.copyright_holder,
            "language": metadata.language,
            "age_range": metadata.age_range,
            "grade_range": metadata.grade_range
        }
        
        try:
            response = await self.make_kdp_request(
                "PUT", 
                f"/projects/{kdp_project_id}/metadata",
                metadata_update
            )
            
            if response.status == 200:
                return {"success": True, "message": "Metadata updated successfully"}
            elif response.status == 404:
                raise HTTPException(status_code=404, detail="KDP project not found")
            else:
                error_text = await response.text()
                raise HTTPException(status_code=response.status, detail=f"Failed to update metadata: {error_text}")
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to update KDP project metadata: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to update metadata: {e}")
    
    async def upload_manuscript(self, publishing_project_id: str, 
                               manuscript_file_path: str, workspace_id: str) -> Dict[str, Any]:
        """Upload formatted manuscript to KDP."""
        await verify_workspace_access(workspace_id, get_current_user().id)
        
        # Get publishing project
        from .project_management_service import PublishingProjectManagementService
        project_service = PublishingProjectManagementService()
        publishing_project = await project_service.get_publishing_project(publishing_project_id, workspace_id)
        
        if not publishing_project.kdp_project_id:
            raise HTTPException(status_code=400, detail="KDP project not created yet")
            
        # Validate file exists and is readable
        file_path = Path(manuscript_file_path)
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="Manuscript file not found")
            
        # Determine file format based on extension
        file_format = "pdf" if file_path.suffix.lower() == ".pdf" else "epub"
        
        # Prepare manuscript upload data
        upload_data = {
            "project_id": publishing_project.kdp_project_id,
            "format": file_format,
            "version": publishing_project.manuscript_version,
            "content_type": "text" if publishing_project.ebook_enabled else "print"
        }
        
        try:
            # Upload file
            with open(manuscript_file_path, 'rb') as file:
                files = {'manuscript': file}
                response = await self.make_kdp_file_request(
                    "POST", 
                    f"/projects/{publishing_project.kdp_project_id}/manuscript",
                    upload_data,
                    files
                )
            
            if response.status == 200:
                upload_result = await response.json()
                
                # Update project status
                await project_service.update_publishing_project(
                    publishing_project_id, 
                    workspace_id,
                    {
                        "status": "manuscript_uploaded",
                        "publishing_stage": "metadata",
                        "last_sync": datetime.utcnow()
                    }
                )
                
                logger.info(f"Uploaded manuscript for project {publishing_project_id}")
                
                return {
                    "success": True,
                    "upload_id": upload_result["upload_id"],
                    "processing_status": upload_result["status"],
                    "estimated_processing_time": upload_result.get("estimated_processing_time", "1-24 hours")
                }
            elif response.status == 400:
                error_data = await response.json()
                raise HTTPException(status_code=400, detail=f"Invalid manuscript file: {error_data}")
            elif response.status == 413:
                raise HTTPException(status_code=413, detail="Manuscript file too large")
            else:
                error_text = await response.text()
                raise HTTPException(status_code=response.status, detail=f"Failed to upload manuscript: {error_text}")
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Manuscript upload failed: {e}")
            raise HTTPException(status_code=500, detail=f"Manuscript upload failed: {e}")
    
    async def upload_cover(self, publishing_project_id: str, cover_file_path: str,
                         workspace_id: str) -> Dict[str, Any]:
        """Upload book cover to KDP."""
        await verify_workspace_access(workspace_id, get_current_user().id)
        
        from .project_management_service import PublishingProjectManagementService
        project_service = PublishingProjectManagementService()
        publishing_project = await project_service.get_publishing_project(publishing_project_id, workspace_id)
        
        if not publishing_project.kdp_project_id:
            raise HTTPException(status_code=400, detail="KDP project not created yet")
            
        # Validate file exists
        file_path = Path(cover_file_path)
        if not file_path.exists():
            raise HTTPException(status_code=404, detail="Cover file not found")
        
        # Validate cover requirements
        validation_result = await self.validate_cover_requirements(cover_file_path, publishing_project)
        if not validation_result["valid"]:
            raise HTTPException(status_code=400, detail=f"Cover validation failed: {validation_result['errors']}")
        
        try:
            # Upload cover
            with open(cover_file_path, 'rb') as file:
                files = {'cover': file}
                response = await self.make_kdp_file_request(
                    "POST",
                    f"/projects/{publishing_project.kdp_project_id}/cover",
                    {"format": "pdf"},
                    files
                )
            
            if response.status == 200:
                # Update project status
                await project_service.update_publishing_project(
                    publishing_project_id,
                    workspace_id,
                    {
                        "cover_approved": True,
                        "publishing_stage": "pricing" if publishing_project.status == "metadata" else publishing_project.publishing_stage,
                        "last_sync": datetime.utcnow()
                    }
                )
                
                logger.info(f"Uploaded cover for project {publishing_project_id}")
                return {"success": True, "cover_uploaded": True}
            else:
                error_text = await response.text()
                raise HTTPException(status_code=response.status, detail=f"Failed to upload cover: {error_text}")
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Cover upload failed: {e}")
            raise HTTPException(status_code=500, detail=f"Cover upload failed: {e}")
    
    async def validate_cover_requirements(self, cover_file_path: str, 
                                        publishing_project: "PublishingProject") -> Dict[str, Any]:
        """Validate cover meets KDP requirements."""
        try:
            from PIL import Image
            
            errors = []
            warnings = []
            
            with Image.open(cover_file_path) as img:
                width, height = img.size
                dpi = img.info.get('dpi', (300, 300))
                
                # KDP cover requirements
                min_width = 1563  # 300 DPI for 5.25" width
                min_height = 2500  # 300 DPI for 8.25" height
                
                if width < min_width:
                    errors.append(f"Cover width {width}px too small (minimum {min_width}px)")
                    
                if height < min_height:
                    errors.append(f"Cover height {height}px too small (minimum {min_height}px)")
                
                # DPI check
                if dpi[0] < 300 or dpi[1] < 300:
                    warnings.append("Cover DPI below 300 may result in poor print quality")
                
                # Aspect ratio check
                aspect_ratio = width / height
                if aspect_ratio < 0.6 or aspect_ratio > 0.7:
                    warnings.append("Cover aspect ratio outside typical book proportions")
                
                # File format
                if img.format not in ['JPEG', 'PNG', 'PDF']:
                    errors.append(f"Unsupported cover format: {img.format}")
            
            return {
                "valid": len(errors) == 0,
                "errors": errors,
                "warnings": warnings
            }
            
        except Exception as e:
            return {
                "valid": False,
                "errors": [f"Cover validation error: {e}"],
                "warnings": []
            }
    
    async def publish_book(self, publishing_project_id: str, workspace_id: str) -> Dict[str, Any]:
        """Publish book to Amazon marketplace."""
        await verify_workspace_access(workspace_id, get_current_user().id)
        
        from .project_management_service import PublishingProjectManagementService
        project_service = PublishingProjectManagementService()
        publishing_project = await project_service.get_publishing_project(publishing_project_id, workspace_id)
        
        if not publishing_project.kdp_project_id:
            raise HTTPException(status_code=400, detail="KDP project not created yet")
        
        # Pre-publishing validation
        validation_result = await project_service.get_project_validation_status(publishing_project_id, workspace_id)
        if not validation_result["project_ready"]:
            raise HTTPException(
                status_code=400, 
                detail=f"Project not ready for publishing. Validation score: {validation_result['overall_score']}%"
            )
        
        # Submit for publishing
        publish_data = {
            "project_id": publishing_project.kdp_project_id,
            "print_enabled": publishing_project.print_enabled,
            "ebook_enabled": publishing_project.ebook_enabled,
            "hardcover_enabled": publishing_project.hardcover_enabled,
            "release_date": publishing_project.publication_date.isoformat() if publishing_project.publication_date else None,
            "pricing": {
                "print_price": publishing_project.print_price,
                "ebook_price": publishing_project.ebook_price
            }
        }
        
        try:
            response = await self.make_kdp_request(
                "POST",
                f"/projects/{publishing_project.kdp_project_id}/publish",
                publish_data
            )
            
            if response.status == 200:
                publish_result = await response.json()
                
                # Update project status
                await project_service.update_publishing_project(
                    publishing_project_id,
                    workspace_id,
                    {
                        "status": "publishing",
                        "publishing_stage": "review",
                        "live_date": datetime.utcnow(),
                        "last_sync": datetime.utcnow()
                    }
                )
                
                logger.info(f"Submitted project {publishing_project_id} for publishing")
                
                return {
                    "success": True,
                    "publishing_status": publish_result["status"],
                    "estimated_live_date": publish_result.get("estimated_live_date"),
                    "review_time": publish_result.get("review_time", "24-72 hours")
                }
            elif response.status == 400:
                error_data = await response.json()
                raise HTTPException(status_code=400, detail=f"Publishing validation failed: {error_data}")
            else:
                error_text = await response.text()
                raise HTTPException(status_code=response.status, detail=f"Failed to publish book: {error_text}")
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Publishing failed: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to publish book: {e}")
    
    async def unpublish_book(self, publishing_project_id: str, workspace_id: str) -> Dict[str, Any]:
        """Unpublish book from Amazon marketplace."""
        await verify_workspace_access(workspace_id, get_current_user().id)
        
        from .project_management_service import PublishingProjectManagementService
        project_service = PublishingProjectManagementService()
        publishing_project = await project_service.get_publishing_project(publishing_project_id, workspace_id)
        
        if not publishing_project.kdp_project_id:
            raise HTTPException(status_code=400, detail="KDP project not found")
            
        try:
            response = await self.make_kdp_request(
                "POST",
                f"/projects/{publishing_project.kdp_project_id}/unpublish",
                {"reason": "Author request"}
            )
            
            if response.status == 200:
                # Update project status
                await project_service.update_publishing_project(
                    publishing_project_id,
                    workspace_id,
                    {
                        "status": "draft",
                        "publishing_stage": "review",
                        "print_published": False,
                        "ebook_published": False,
                        "last_sync": datetime.utcnow()
                    }
                )
                
                logger.info(f"Unpublished project {publishing_project_id}")
                return {"success": True, "message": "Book unpublished successfully"}
            else:
                error_text = await response.text()
                raise HTTPException(status_code=response.status, detail=f"Failed to unpublish: {error_text}")
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Unpublishing failed: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to unpublish book: {e}")