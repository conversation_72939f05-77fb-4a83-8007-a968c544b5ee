from datetime import datetime
from typing import Optional, Any
from uuid import uuid4

from pydantic import Field, validator
from sqlmodel import Column, DateTime, SQLModel, JSON

from langflow.services.database.models.base import orjson_dumps


class ChapterTemplateBase(SQLModel):
    name: str = Field(min_length=1, max_length=255, description="Template name")
    genre: str = Field(min_length=1, max_length=100, description="Genre for this template")
    description: Optional[str] = Field(None, description="Template description")

    # Guidelines
    word_count_target: Optional[int] = Field(None, ge=0, description="Target word count for chapters using this template")
    recommended_scene_count: Optional[int] = Field(None, ge=1, description="Recommended number of scenes")
    pacing_guidelines: Optional[str] = Field(None, description="Pacing guidelines for this template")
    structure_notes: Optional[str] = Field(None, description="Additional structural notes")

    # Visibility
    is_public: bool = Field(default=False, description="Whether template is public")


class ChapterTemplate(ChapterTemplateBase, table=True):
    __tablename__ = "chapter_template"
    
    id: str = Field(primary_key=True, default_factory=lambda: str(uuid4()))
    
    # Metadata
    usage_count: int = Field(default=0, description="Number of times template has been used")
    created_by: str = Field(foreign_key="user.id", description="User who created this template")
    workspace_id: str = Field(foreign_key="workspace.id", description="Workspace this template belongs to")
    
    # Timestamps
    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime(timezone=True), nullable=False)
    )
    updated_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime(timezone=True), nullable=False)
    )

    @validator('scene_templates')
    def validate_scene_templates(cls, v):
        """Validate scene template structure."""
        if not isinstance(v, list):
            raise ValueError('scene_templates must be a list')
        
        for i, scene in enumerate(v):
            if not isinstance(scene, dict):
                raise ValueError(f'Scene template {i} must be a dictionary')
            
            # Required fields
            if 'title' not in scene:
                raise ValueError(f'Scene template {i} must have a title')
            
            # Optional but validated fields
            if 'word_count_target' in scene and scene['word_count_target'] is not None:
                if not isinstance(scene['word_count_target'], int) or scene['word_count_target'] < 0:
                    raise ValueError(f'Scene template {i} word_count_target must be a positive integer')
            
            if 'order' in scene and scene['order'] is not None:
                if not isinstance(scene['order'], int) or scene['order'] < 1:
                    raise ValueError(f'Scene template {i} order must be a positive integer')
        
        return v

    @validator('word_count_target')
    def validate_word_count_target(cls, v):
        """Validate word count target."""
        if v is not None and v < 0:
            raise ValueError('word_count_target must be positive')
        return v

    @validator('recommended_scene_count')
    def validate_recommended_scene_count(cls, v):
        """Validate recommended scene count."""
        if v is not None and v < 1:
            raise ValueError('recommended_scene_count must be at least 1')
        return v

    def dict(self, **kwargs):
        """Override dict method to handle JSON serialization."""
        data = super().dict(**kwargs)
        
        # Ensure JSON fields are properly serialized
        if 'default_scenes' in data and data['default_scenes']:
            data['default_scenes'] = orjson_dumps(data['default_scenes'])
        if 'scene_templates' in data and data['scene_templates']:
            data['scene_templates'] = orjson_dumps(data['scene_templates'])
            
        return data


class ChapterTemplateCreate(ChapterTemplateBase):
    """Schema for creating a new chapter template."""
    pass


class ChapterTemplateUpdate(SQLModel):
    """Schema for updating a chapter template."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    genre: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = None
    default_scenes: Optional[list[dict[str, Any]]] = Field(None, sa_column=Column(JSON))
    scene_templates: Optional[list[dict[str, Any]]] = Field(None, sa_column=Column(JSON))
    word_count_target: Optional[int] = Field(None, ge=0)
    recommended_scene_count: Optional[int] = Field(None, ge=1)
    pacing_guidelines: Optional[str] = None
    structure_notes: Optional[str] = None
    is_public: Optional[bool] = None


class ChapterTemplateRead(ChapterTemplateBase):
    """Schema for reading chapter template data."""
    id: str
    usage_count: int
    created_by: str
    workspace_id: str
    created_at: datetime
    updated_at: datetime


class ChapterTemplateWithStats(ChapterTemplateRead):
    """Chapter template with additional statistics."""
    chapters_created: int = Field(default=0, description="Number of chapters created from this template")
    last_used: Optional[datetime] = Field(None, description="When template was last used")
    creator_name: Optional[str] = Field(None, description="Name of template creator")


# Scene template structure
class SceneTemplateStructure(SQLModel):
    """Structure for individual scene templates within a chapter template."""
    id: str = Field(description="Unique identifier for this scene template")
    title: str = Field(min_length=1, max_length=255, description="Scene title")
    purpose: Optional[str] = Field(None, description="Purpose of this scene")
    scene_type: Optional[str] = Field(None, description="Type of scene (action, dialogue, etc.)")
    word_count_target: Optional[int] = Field(None, ge=0, description="Target word count for this scene")
    mood: Optional[str] = Field(None, description="Intended mood for this scene")
    conflict_type: Optional[str] = Field(None, description="Type of conflict in this scene")
    description: Optional[str] = Field(None, description="Additional scene description")
    order: int = Field(ge=1, description="Order of this scene in the chapter")

    # Narrative elements
    location_suggestion: Optional[str] = Field(None, description="Suggested location for this scene")
    time_suggestion: Optional[str] = Field(None, description="Suggested time for this scene")
    character_suggestions: list[str] = Field(default_factory=list, sa_column=Column(JSON), description="Suggested characters for this scene")

    # Plot elements
    plot_points: list[str] = Field(default_factory=list, sa_column=Column(JSON), description="Plot points covered in this scene")
    foreshadowing_elements: list[str] = Field(default_factory=list, sa_column=Column(JSON), description="Foreshadowing elements")

    # Writing guidance
    writing_tips: Optional[str] = Field(None, description="Writing tips for this scene")
    common_mistakes: Optional[str] = Field(None, description="Common mistakes to avoid")


# Export schema for templates
class ChapterTemplateExport(SQLModel):
    """Schema for exporting/importing chapter templates."""
    name: str
    genre: str
    description: Optional[str] = None
    scene_templates: list[dict[str, Any]] = Field(default_factory=list, sa_column=Column(JSON))
    word_count_target: Optional[int] = None
    recommended_scene_count: Optional[int] = None
    pacing_guidelines: Optional[str] = None
    structure_notes: Optional[str] = None

    # Metadata for import
    export_version: str = Field(default="1.0", description="Export format version")
    exported_at: datetime = Field(default_factory=datetime.utcnow, description="When template was exported")
    exported_by: Optional[str] = Field(None, description="User who exported the template")