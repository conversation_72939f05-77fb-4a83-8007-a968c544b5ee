"""
Publishing Pipeline Optimization Service.

This service provides optimization capabilities for the Amazon KDP publishing pipeline:
- Performance monitoring and metrics collection
- Database operation optimization
- API call batching and connection pooling
- Memory management for large manuscripts
- Concurrent operation coordination
- Caching strategies for frequently accessed data
"""

import asyncio
import time
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from datetime import datetime, timed<PERSON>ta
import json
from concurrent.futures import ThreadPoolExecutor
import weakref
import gc
from contextlib import asynccontextmanager

from langflow.services.base import Service


@dataclass
class PerformanceMetrics:
    """Performance metrics for operations."""
    operation_name: str
    start_time: float
    end_time: float
    duration: float
    memory_used: int
    success: bool
    error_message: Optional[str] = None
    
    @property
    def duration_ms(self) -> float:
        """Get duration in milliseconds."""
        return self.duration * 1000
    
    @property
    def memory_mb(self) -> float:
        """Get memory usage in MB."""
        return self.memory_used / (1024 * 1024)


@dataclass
class OptimizationConfig:
    """Configuration for pipeline optimization."""
    # Database optimization
    batch_size: int = 50
    connection_pool_size: int = 10
    query_timeout: float = 30.0
    
    # API optimization
    api_rate_limit: int = 100  # requests per minute
    retry_attempts: int = 3
    retry_backoff: float = 1.0
    
    # Memory optimization
    max_memory_usage: int = 512 * 1024 * 1024  # 512MB
    gc_threshold: int = 100  # operations before garbage collection
    
    # Caching
    cache_ttl: int = 3600  # 1 hour
    max_cache_size: int = 1000  # items
    
    # Concurrency
    max_concurrent_operations: int = 20
    chapter_processing_batch_size: int = 10


class PerformanceMonitor:
    """Monitor and track performance metrics."""
    
    def __init__(self):
        self.metrics: List[PerformanceMetrics] = []
        self.operation_counts: Dict[str, int] = {}
        self.active_operations: Dict[str, float] = {}
    
    @asynccontextmanager
    async def monitor_operation(self, operation_name: str):
        """Context manager to monitor operation performance."""
        import psutil
        process = psutil.Process()
        
        start_time = time.time()
        start_memory = process.memory_info().rss
        operation_id = f"{operation_name}_{int(start_time * 1000)}"
        
        self.active_operations[operation_id] = start_time
        success = False
        error_message = None
        
        try:
            yield operation_id
            success = True
        except Exception as e:
            error_message = str(e)
            raise
        finally:
            end_time = time.time()
            end_memory = process.memory_info().rss
            duration = end_time - start_time
            memory_used = end_memory - start_memory
            
            metric = PerformanceMetrics(
                operation_name=operation_name,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                memory_used=memory_used,
                success=success,
                error_message=error_message
            )
            
            self.metrics.append(metric)
            self.operation_counts[operation_name] = self.operation_counts.get(operation_name, 0) + 1
            self.active_operations.pop(operation_id, None)
    
    def get_operation_stats(self, operation_name: str) -> Dict[str, Any]:
        """Get statistics for a specific operation."""
        operation_metrics = [m for m in self.metrics if m.operation_name == operation_name]
        
        if not operation_metrics:
            return {"count": 0}
        
        durations = [m.duration for m in operation_metrics]
        memory_usage = [m.memory_used for m in operation_metrics]
        success_rate = sum(1 for m in operation_metrics if m.success) / len(operation_metrics)
        
        return {
            "count": len(operation_metrics),
            "avg_duration": sum(durations) / len(durations),
            "min_duration": min(durations),
            "max_duration": max(durations),
            "avg_memory": sum(memory_usage) / len(memory_usage),
            "success_rate": success_rate,
            "total_memory": sum(memory_usage)
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get overall performance summary."""
        total_operations = len(self.metrics)
        successful_operations = sum(1 for m in self.metrics if m.success)
        
        return {
            "total_operations": total_operations,
            "successful_operations": successful_operations,
            "success_rate": successful_operations / total_operations if total_operations > 0 else 0,
            "active_operations": len(self.active_operations),
            "operation_types": list(self.operation_counts.keys()),
            "operation_counts": self.operation_counts
        }


class DatabaseOptimizer:
    """Optimize database operations for better performance."""
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
        self.connection_pool = None
        self.batch_operations = []
        self.batch_lock = asyncio.Lock()
    
    async def initialize_connection_pool(self, database_service):
        """Initialize database connection pool."""
        # Implementation would depend on specific database provider
        if hasattr(database_service, 'create_pool'):
            self.connection_pool = await database_service.create_pool(
                max_connections=self.config.connection_pool_size
            )
    
    async def batch_create_documents(self, collection: str, documents: List[Dict[str, Any]], 
                                   workspace_id: str, database_service) -> List[Dict[str, Any]]:
        """Batch create documents for better performance."""
        results = []
        
        # Process in batches to avoid overwhelming the database
        for i in range(0, len(documents), self.config.batch_size):
            batch = documents[i:i + self.config.batch_size]
            
            if database_service.database_type == "firebase":
                # Firebase batch write
                batch_results = await self._firebase_batch_create(
                    collection, batch, workspace_id, database_service
                )
            else:
                # SQL batch insert
                batch_results = await self._sql_batch_create(
                    collection, batch, workspace_id, database_service
                )
            
            results.extend(batch_results)
            
            # Small delay to prevent overwhelming the database
            await asyncio.sleep(0.01)
        
        return results
    
    async def _firebase_batch_create(self, collection: str, documents: List[Dict[str, Any]], 
                                   workspace_id: str, database_service) -> List[Dict[str, Any]]:
        """Firebase-specific batch create implementation."""
        batch_ref = database_service.db.batch()
        doc_refs = []
        
        for doc_data in documents:
            doc_ref = database_service.db.collection(f"workspaces/{workspace_id}/{collection}").document()
            batch_ref.set(doc_ref, doc_data)
            doc_refs.append(doc_ref)
        
        await batch_ref.commit()
        
        # Return created document references
        return [{"id": doc_ref.id, **doc_data} for doc_ref, doc_data in zip(doc_refs, documents)]
    
    async def _sql_batch_create(self, table: str, documents: List[Dict[str, Any]], 
                               workspace_id: str, database_service) -> List[Dict[str, Any]]:
        """SQL-specific batch create implementation."""
        # Add workspace_id to all documents
        for doc in documents:
            doc["workspace_id"] = workspace_id
        
        # Use bulk insert
        await database_service.bulk_insert(table, documents)
        
        return documents
    
    async def optimize_query(self, query_params: Dict[str, Any], database_service) -> Dict[str, Any]:
        """Optimize database queries for better performance."""
        # Add query optimization logic
        optimized_params = query_params.copy()
        
        # Add indexing hints
        if "filters" in optimized_params:
            optimized_params["use_index"] = True
        
        # Limit result size for performance
        if "limit" not in optimized_params:
            optimized_params["limit"] = 100
        
        # Add timeout
        optimized_params["timeout"] = self.config.query_timeout
        
        return optimized_params


class APIOptimizer:
    """Optimize API calls and external service interactions."""
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
        self.rate_limiter = asyncio.Semaphore(self.config.api_rate_limit // 60)  # Per second
        self.connection_pool = None
    
    async def initialize_connection_pool(self):
        """Initialize HTTP connection pool."""
        import aiohttp
        connector = aiohttp.TCPConnector(
            limit=self.config.connection_pool_size,
            limit_per_host=self.config.connection_pool_size // 2
        )
        self.connection_pool = aiohttp.ClientSession(connector=connector)
    
    async def batch_api_calls(self, api_calls: List[Callable], max_concurrent: int = None) -> List[Any]:
        """Execute API calls in optimized batches."""
        if max_concurrent is None:
            max_concurrent = self.config.max_concurrent_operations
        
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def limited_call(call):
            async with semaphore:
                async with self.rate_limiter:
                    return await call()
        
        # Execute all calls concurrently with limits
        tasks = [limited_call(call) for call in api_calls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        return results
    
    async def retry_with_backoff(self, operation: Callable, max_retries: int = None) -> Any:
        """Retry operation with exponential backoff."""
        if max_retries is None:
            max_retries = self.config.retry_attempts
        
        last_exception = None
        
        for attempt in range(max_retries + 1):
            try:
                return await operation()
            except Exception as e:
                last_exception = e
                
                if attempt == max_retries:
                    break
                
                # Exponential backoff
                delay = self.config.retry_backoff * (2 ** attempt)
                await asyncio.sleep(delay)
        
        raise last_exception
    
    async def cleanup(self):
        """Clean up connection pool."""
        if self.connection_pool:
            await self.connection_pool.close()


class MemoryOptimizer:
    """Optimize memory usage for large manuscript processing."""
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
        self.operation_counter = 0
        self.memory_warnings = []
    
    async def process_large_content_in_chunks(self, content: List[Any], 
                                            processor: Callable, 
                                            chunk_size: int = None) -> List[Any]:
        """Process large content in memory-efficient chunks."""
        if chunk_size is None:
            chunk_size = self.config.chapter_processing_batch_size
        
        results = []
        
        for i in range(0, len(content), chunk_size):
            chunk = content[i:i + chunk_size]
            
            # Process chunk
            chunk_result = await processor(chunk)
            results.extend(chunk_result if isinstance(chunk_result, list) else [chunk_result])
            
            # Force garbage collection periodically
            self.operation_counter += 1
            if self.operation_counter >= self.config.gc_threshold:
                gc.collect()
                self.operation_counter = 0
            
            # Check memory usage
            await self._check_memory_usage()
        
        return results
    
    async def _check_memory_usage(self):
        """Check current memory usage and warn if high."""
        import psutil
        process = psutil.Process()
        memory_usage = process.memory_info().rss
        
        if memory_usage > self.config.max_memory_usage:
            warning = {
                "timestamp": datetime.utcnow(),
                "memory_usage": memory_usage,
                "limit": self.config.max_memory_usage
            }
            self.memory_warnings.append(warning)
            
            # Force garbage collection
            gc.collect()
            
            logging.warning(f"High memory usage detected: {memory_usage / 1024 / 1024:.2f}MB")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        import psutil
        process = psutil.Process()
        
        return {
            "current_memory": process.memory_info().rss,
            "memory_percent": process.memory_percent(),
            "memory_warnings": len(self.memory_warnings),
            "gc_operations": self.operation_counter
        }


class CacheManager:
    """Manage caching for frequently accessed data."""
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_timestamps: Dict[str, datetime] = {}
        self.access_counts: Dict[str, int] = {}
    
    async def get(self, key: str) -> Optional[Any]:
        """Get item from cache."""
        if key not in self.cache:
            return None
        
        # Check if expired
        if self._is_expired(key):
            await self.remove(key)
            return None
        
        # Update access count
        self.access_counts[key] = self.access_counts.get(key, 0) + 1
        
        return self.cache[key]["data"]
    
    async def set(self, key: str, data: Any, ttl: Optional[int] = None) -> None:
        """Set item in cache."""
        if ttl is None:
            ttl = self.config.cache_ttl
        
        # Check cache size limit
        if len(self.cache) >= self.config.max_cache_size:
            await self._evict_least_recently_used()
        
        self.cache[key] = {
            "data": data,
            "ttl": ttl
        }
        self.cache_timestamps[key] = datetime.utcnow()
        self.access_counts[key] = 1
    
    async def remove(self, key: str) -> None:
        """Remove item from cache."""
        self.cache.pop(key, None)
        self.cache_timestamps.pop(key, None)
        self.access_counts.pop(key, None)
    
    def _is_expired(self, key: str) -> bool:
        """Check if cache item is expired."""
        if key not in self.cache_timestamps:
            return True
        
        timestamp = self.cache_timestamps[key]
        ttl = self.cache[key]["ttl"]
        expiry_time = timestamp + timedelta(seconds=ttl)
        
        return datetime.utcnow() > expiry_time
    
    async def _evict_least_recently_used(self) -> None:
        """Evict least recently used item from cache."""
        if not self.access_counts:
            return
        
        # Find item with lowest access count
        lru_key = min(self.access_counts.keys(), key=lambda k: self.access_counts[k])
        await self.remove(lru_key)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_items = len(self.cache)
        expired_items = sum(1 for key in self.cache.keys() if self._is_expired(key))
        
        return {
            "total_items": total_items,
            "expired_items": expired_items,
            "hit_rate": sum(self.access_counts.values()) / max(total_items, 1),
            "memory_usage": sum(len(str(item)) for item in self.cache.values())
        }


class PublishingPipelineOptimizer(Service):
    """Main optimization service for publishing pipeline."""
    
    def __init__(self, config: Optional[OptimizationConfig] = None):
        super().__init__()
        self.config = config or OptimizationConfig()
        
        self.performance_monitor = PerformanceMonitor()
        self.database_optimizer = DatabaseOptimizer(self.config)
        self.api_optimizer = APIOptimizer(self.config)
        self.memory_optimizer = MemoryOptimizer(self.config)
        self.cache_manager = CacheManager(self.config)
        
        self.thread_pool = ThreadPoolExecutor(max_workers=self.config.max_concurrent_operations)
    
    async def initialize(self):
        """Initialize the optimization service."""
        await self.api_optimizer.initialize_connection_pool()
        logging.info("Publishing pipeline optimizer initialized")
    
    async def optimize_manuscript_processing(self, chapters: List[Dict[str, Any]], 
                                           processor: Callable) -> List[Any]:
        """Optimize manuscript processing for large documents."""
        async with self.performance_monitor.monitor_operation("manuscript_processing"):
            return await self.memory_optimizer.process_large_content_in_chunks(
                chapters, processor, self.config.chapter_processing_batch_size
            )
    
    async def optimize_database_operations(self, operation_type: str, data: List[Dict[str, Any]], 
                                         database_service, workspace_id: str) -> List[Any]:
        """Optimize database operations."""
        cache_key = f"db_op_{operation_type}_{workspace_id}_{hash(str(data))}"
        
        # Check cache first
        cached_result = await self.cache_manager.get(cache_key)
        if cached_result:
            return cached_result
        
        async with self.performance_monitor.monitor_operation(f"database_{operation_type}"):
            if operation_type == "batch_create":
                result = await self.database_optimizer.batch_create_documents(
                    "publishing_projects", data, workspace_id, database_service
                )
            else:
                # Handle other operation types
                result = data
            
            # Cache result
            await self.cache_manager.set(cache_key, result)
            return result
    
    async def optimize_api_calls(self, api_operations: List[Callable]) -> List[Any]:
        """Optimize external API calls."""
        async with self.performance_monitor.monitor_operation("api_batch"):
            return await self.api_optimizer.batch_api_calls(api_operations)
    
    async def optimize_concurrent_operations(self, operations: List[Callable]) -> List[Any]:
        """Optimize concurrent operations with proper resource management."""
        semaphore = asyncio.Semaphore(self.config.max_concurrent_operations)
        
        async def limited_operation(operation):
            async with semaphore:
                async with self.performance_monitor.monitor_operation("concurrent_op"):
                    return await operation()
        
        tasks = [limited_operation(op) for op in operations]
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """Get comprehensive optimization report."""
        return {
            "performance_summary": self.performance_monitor.get_performance_summary(),
            "memory_stats": self.memory_optimizer.get_memory_stats(),
            "cache_stats": self.cache_manager.get_cache_stats(),
            "optimization_config": {
                "batch_size": self.config.batch_size,
                "max_concurrent_operations": self.config.max_concurrent_operations,
                "cache_ttl": self.config.cache_ttl,
                "max_memory_usage": self.config.max_memory_usage
            }
        }
    
    async def cleanup(self):
        """Clean up resources."""
        await self.api_optimizer.cleanup()
        self.thread_pool.shutdown(wait=True)
        logging.info("Publishing pipeline optimizer cleaned up")


# Factory function for easy service instantiation
def create_publishing_pipeline_optimizer(config: Optional[OptimizationConfig] = None) -> PublishingPipelineOptimizer:
    """Create and return a PublishingPipelineOptimizer instance."""
    return PublishingPipelineOptimizer(config)