"""
Integration tests for Amazon KDP Publishing Workflow.

This test suite covers the complete publishing workflow integration including:
- End-to-end publishing pipeline
- Manuscript formatting and upload
- Metadata management integration
- Cover design integration
- Publishing validation and automation
- Database provider compatibility (Firebase primary, SQL fallback)

These tests validate the complete workflow from book creation to KDP publishing.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch, mock_open
from datetime import datetime, date
import tempfile
import json
from pathlib import Path
import aiohttp

from fastapi import HTTPException
from fastapi.testclient import TestClient

from langflow.services.publishing.publishing_workflow_service import PublishingWorkflowService
from langflow.services.publishing.kdp_service import AmazonKDPService
from langflow.services.publishing.manuscript_formatting_service import ManuscriptFormattingService
from langflow.services.publishing.metadata_management_service import MetadataManagementService
from langflow.services.database.models.book.model import Book
from langflow.services.database.models.publishing.publishing_project import PublishingProject
from langflow.schema.publishing import BookMetadata, ManuscriptFormat, PricingStrategy
from langflow.services.deps import get_database_service


@pytest.fixture
def mock_database_service():
    """Mock database service that supports both Firebase and SQL."""
    service = AsyncMock()
    service.database_type = "firebase"  # Default to Firebase
    
    # Mock Firebase operations
    service.create_document = AsyncMock()
    service.get_document = AsyncMock()
    service.update_document = AsyncMock()
    service.delete_document = AsyncMock()
    service.query_documents = AsyncMock()
    
    # Mock SQL operations for fallback
    service.add = AsyncMock()
    service.commit = AsyncMock()
    service.rollback = AsyncMock()
    service.get = AsyncMock()
    
    return service


@pytest.fixture
def mock_workspace():
    """Create a mock workspace for testing."""
    return {
        "id": "test_workspace_123",
        "name": "Test Publishing Workspace",
        "owner_id": "test_user_456",
        "database_provider": "firebase"  # Default to Firebase
    }


@pytest.fixture
def sample_book():
    """Create a sample book for testing."""
    return Book(
        id="test_book_789",
        title="Integration Test Novel",
        description="A comprehensive novel for testing the publishing workflow",
        author="Test Author",
        genre="Science Fiction",
        status="completed",
        word_count=75000,
        workspace_id="test_workspace_123"
    )


@pytest.fixture
def sample_publishing_project(sample_book):
    """Create a sample publishing project."""
    return PublishingProject(
        id="test_project_123",
        book_id=sample_book.id,
        status="draft",
        publishing_stage="preparation",
        manuscript_version="1.0",
        word_count=sample_book.word_count,
        page_count=300,
        print_enabled=True,
        ebook_enabled=True,
        hardcover_enabled=False,
        publication_date=date(2024, 8, 1),
        workspace_id="test_workspace_123"
    )


@pytest.fixture
def sample_book_metadata():
    """Create sample book metadata."""
    return BookMetadata(
        title="Integration Test Novel",
        subtitle="A Comprehensive Testing Story",
        primary_author="Test Author",
        co_authors=["Co-Author 1"],
        description="A comprehensive novel designed specifically for testing the complete Amazon KDP publishing workflow integration.",
        primary_category="Fiction > Science Fiction",
        secondary_category="Fiction > Adventure",
        keywords=["integration", "test", "novel", "scifi", "adventure", "kdp", "publishing"],
        language="English",
        copyright_year=2024,
        copyright_holder="Test Author",
        workspace_id="test_workspace_123"
    )


@pytest.fixture
def sample_manuscript_format():
    """Create sample manuscript format."""
    return ManuscriptFormat(
        id="test_format_456",
        publishing_project_id="test_project_123",
        format_type="print",
        trim_size="6x9",
        paper_type="white",
        binding_type="paperback",
        margin_top=0.75,
        margin_bottom=0.75,
        margin_inside=0.75,
        margin_outside=0.75,
        font_family="Times New Roman",
        font_size=11,
        line_spacing=1.15,
        workspace_id="test_workspace_123"
    )


class TestPublishingWorkflowIntegration:
    """Test complete publishing workflow integration."""
    
    @patch('langflow.services.database.config.get_database_service')
    @patch('langflow.services.deps.get_current_user')
    @patch('langflow.services.deps.verify_workspace_access')
    async def test_complete_publishing_workflow_firebase(
        self, mock_verify_access, mock_get_user, mock_get_db_service,
        mock_database_service, mock_workspace, sample_book, 
        sample_publishing_project, sample_book_metadata, sample_manuscript_format
    ):
        """Test complete publishing workflow with Firebase backend."""
        # Setup mocks
        mock_get_user.return_value = Mock(id="test_user_456")
        mock_verify_access.return_value = None
        mock_get_db_service.return_value = mock_database_service
        mock_database_service.database_type = "firebase"
        
        # Mock Firebase document operations
        mock_database_service.create_document.side_effect = [
            {"id": sample_publishing_project.id},  # Publishing project creation
            {"id": sample_book_metadata.id},       # Metadata creation
            {"id": sample_manuscript_format.id}    # Format creation
        ]
        
        mock_database_service.get_document.side_effect = [
            {"id": sample_book.id, **sample_book.dict()},
            {"id": sample_publishing_project.id, **sample_publishing_project.dict()},
            {"id": sample_book_metadata.id, **sample_book_metadata.dict()},
            {"id": sample_manuscript_format.id, **sample_manuscript_format.dict()}
        ]
        
        # Initialize services
        workflow_service = PublishingWorkflowService()
        kdp_service = AmazonKDPService()
        formatting_service = ManuscriptFormattingService()
        metadata_service = MetadataManagementService()
        
        # Mock KDP API calls
        with patch.object(kdp_service, 'create_kdp_project') as mock_create_project, \
             patch.object(kdp_service, 'upload_manuscript') as mock_upload_manuscript, \
             patch.object(kdp_service, 'upload_cover') as mock_upload_cover, \
             patch.object(kdp_service, 'publish_book') as mock_publish_book, \
             patch.object(formatting_service, 'format_manuscript_for_print') as mock_format_manuscript, \
             patch.object(metadata_service, 'validate_metadata_completeness') as mock_validate_metadata:
            
            # Configure mock responses
            mock_create_project.return_value = {
                "success": True,
                "kdp_project_id": "kdp_project_789",
                "status": "created"
            }
            
            mock_upload_manuscript.return_value = {
                "success": True,
                "upload_id": "upload_123",
                "processing_status": "processing"
            }
            
            mock_upload_cover.return_value = {
                "success": True,
                "cover_uploaded": True
            }
            
            mock_publish_book.return_value = {
                "success": True,
                "publishing_status": "submitted_for_review",
                "estimated_live_date": "2024-08-05T00:00:00Z"
            }
            
            mock_format_manuscript.return_value = "/tmp/formatted_manuscript.pdf"
            
            mock_validate_metadata.return_value = {
                "completeness_score": 95,
                "is_complete": True,
                "missing_fields": [],
                "warnings": []
            }
            
            # Test workflow execution
            # Step 1: Initialize publishing project
            project_result = await workflow_service.initialize_publishing_project(
                sample_book.id,
                ["print", "ebook"],
                mock_workspace["id"]
            )
            
            assert project_result["success"] is True
            assert project_result["project_id"] == sample_publishing_project.id
            
            # Step 2: Create metadata
            metadata_result = await metadata_service.create_book_metadata(
                sample_publishing_project.id,
                sample_book_metadata.dict(),
                mock_workspace["id"]
            )
            
            assert metadata_result["success"] is True
            
            # Step 3: Format manuscript
            formatting_result = await formatting_service.format_manuscript_for_print(
                sample_publishing_project.id,
                sample_manuscript_format,
                mock_workspace["id"]
            )
            
            assert formatting_result.endswith(".pdf")
            
            # Step 4: Create KDP project
            kdp_project_result = await kdp_service.create_kdp_project(
                sample_publishing_project,
                sample_book_metadata,
                mock_workspace["id"]
            )
            
            assert kdp_project_result["success"] is True
            assert kdp_project_result["kdp_project_id"] == "kdp_project_789"
            
            # Step 5: Upload manuscript
            upload_result = await kdp_service.upload_manuscript(
                sample_publishing_project.id,
                formatting_result,
                mock_workspace["id"]
            )
            
            assert upload_result["success"] is True
            
            # Step 6: Upload cover (simulate existing cover)
            cover_result = await kdp_service.upload_cover(
                sample_publishing_project.id,
                "/tmp/test_cover.pdf",
                mock_workspace["id"]
            )
            
            assert cover_result["success"] is True
            
            # Step 7: Publish book
            publish_result = await kdp_service.publish_book(
                sample_publishing_project.id,
                mock_workspace["id"]
            )
            
            assert publish_result["success"] is True
            assert publish_result["publishing_status"] == "submitted_for_review"
            
            # Verify Firebase operations were called
            assert mock_database_service.create_document.call_count >= 3
            assert mock_database_service.get_document.call_count >= 4
    
    @patch('langflow.services.database.config.get_database_service')
    @patch('langflow.services.deps.get_current_user')
    @patch('langflow.services.deps.verify_workspace_access')
    async def test_publishing_workflow_with_sql_fallback(
        self, mock_verify_access, mock_get_user, mock_get_db_service,
        mock_database_service, mock_workspace, sample_book, sample_publishing_project
    ):
        """Test publishing workflow with SQL database fallback."""
        # Setup mocks for SQL database
        mock_get_user.return_value = Mock(id="test_user_456")
        mock_verify_access.return_value = None
        mock_get_db_service.return_value = mock_database_service
        mock_database_service.database_type = "sqlite"
        
        # Mock SQL operations
        mock_database_service.add.return_value = None
        mock_database_service.commit.return_value = None
        mock_database_service.get.return_value = sample_publishing_project
        
        # Mock workspace with SQL provider
        sql_workspace = {**mock_workspace, "database_provider": "sqlite"}
        
        # Initialize workflow service
        workflow_service = PublishingWorkflowService()
        
        # Test project initialization with SQL backend
        with patch.object(workflow_service, 'database_service', mock_database_service):
            result = await workflow_service.initialize_publishing_project(
                sample_book.id,
                ["print", "ebook"],
                sql_workspace["id"]
            )
            
            assert result["success"] is True
            # Verify SQL operations were called
            mock_database_service.add.assert_called()
            mock_database_service.commit.assert_called()
    
    @patch('langflow.services.database.config.get_database_service')
    async def test_database_provider_switching(self, mock_get_db_service, mock_database_service):
        """Test switching between Firebase and SQL database providers."""
        mock_get_db_service.return_value = mock_database_service
        
        # Test Firebase provider
        mock_database_service.database_type = "firebase"
        workflow_service = PublishingWorkflowService()
        
        assert workflow_service.database_service.database_type == "firebase"
        
        # Test SQL provider
        mock_database_service.database_type = "sqlite"
        workflow_service = PublishingWorkflowService()
        
        assert workflow_service.database_service.database_type == "sqlite"
    
    async def test_workflow_error_handling_and_rollback(self, mock_database_service):
        """Test workflow error handling with proper rollback."""
        workflow_service = PublishingWorkflowService()
        
        # Mock database error during project creation
        mock_database_service.create_document.side_effect = Exception("Database connection failed")
        
        with patch.object(workflow_service, 'database_service', mock_database_service):
            with pytest.raises(HTTPException) as exc_info:
                await workflow_service.initialize_publishing_project(
                    "test_book_123",
                    ["print"],
                    "test_workspace_456"
                )
            
            assert exc_info.value.status_code == 500
            assert "Failed to initialize publishing project" in str(exc_info.value.detail)


class TestManuscriptFormattingIntegration:
    """Test manuscript formatting integration with different providers."""
    
    @patch('langflow.services.database.config.get_database_service')
    @patch('langflow.services.deps.verify_workspace_access')
    async def test_manuscript_formatting_quality_validation(
        self, mock_verify_access, mock_get_db_service, mock_database_service,
        sample_publishing_project, sample_manuscript_format
    ):
        """Test manuscript formatting quality validation."""
        mock_verify_access.return_value = None
        mock_get_db_service.return_value = mock_database_service
        
        # Mock book content
        mock_chapters = [
            {
                "id": "chapter_1",
                "title": "Chapter 1: The Beginning",
                "number": 1,
                "content": "This is the beginning of our test novel. " * 100,
                "word_count": 500
            },
            {
                "id": "chapter_2", 
                "title": "Chapter 2: The Middle",
                "number": 2,
                "content": "This is the middle section of our test novel. " * 150,
                "word_count": 750
            }
        ]
        
        mock_database_service.query_documents.return_value = mock_chapters
        mock_database_service.get_document.return_value = {
            "id": sample_publishing_project.id,
            **sample_publishing_project.dict()
        }
        
        formatting_service = ManuscriptFormattingService()
        
        with patch.object(formatting_service, 'generate_pdf') as mock_generate_pdf, \
             patch.object(formatting_service, 'validate_typography_quality') as mock_validate_typography:
            
            mock_generate_pdf.return_value = "/tmp/test_manuscript.pdf"
            mock_validate_typography.return_value = {
                "overall_score": 88,
                "issues": [
                    {"type": "spacing", "severity": "warning", "count": 3},
                    {"type": "punctuation", "severity": "minor", "count": 1}
                ],
                "recommendations": ["Consider adjusting paragraph spacing for better readability"]
            }
            
            # Test formatting with quality validation
            result = await formatting_service.format_manuscript_for_print(
                sample_publishing_project.id,
                sample_manuscript_format,
                "test_workspace_123"
            )
            
            assert result.endswith(".pdf")
            mock_validate_typography.assert_called_once()
    
    @patch('langflow.services.database.config.get_database_service')
    async def test_large_manuscript_performance(
        self, mock_get_db_service, mock_database_service, sample_publishing_project
    ):
        """Test performance with large manuscripts."""
        mock_get_db_service.return_value = mock_database_service
        
        # Mock large book with many chapters
        large_chapters = []
        for i in range(50):  # 50 chapters
            large_chapters.append({
                "id": f"chapter_{i+1}",
                "title": f"Chapter {i+1}: Part {i+1}",
                "number": i+1,
                "content": "This is a long chapter content. " * 1000,  # ~6000 words per chapter
                "word_count": 6000
            })
        
        mock_database_service.query_documents.return_value = large_chapters
        mock_database_service.get_document.return_value = {
            "id": sample_publishing_project.id,
            "word_count": 300000,  # 300k words
            **sample_publishing_project.dict()
        }
        
        formatting_service = ManuscriptFormattingService()
        
        with patch.object(formatting_service, 'generate_pdf') as mock_generate_pdf:
            mock_generate_pdf.return_value = "/tmp/large_manuscript.pdf"
            
            import time
            start_time = time.time()
            
            # Test formatting large manuscript
            format_spec = ManuscriptFormat(
                publishing_project_id=sample_publishing_project.id,
                format_type="print",
                trim_size="6x9",
                workspace_id="test_workspace_123"
            )
            
            result = await formatting_service.format_manuscript_for_print(
                sample_publishing_project.id,
                format_spec,
                "test_workspace_123"
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            assert result.endswith(".pdf")
            # Should complete within reasonable time (mock operations should be fast)
            assert processing_time < 5.0


class TestMetadataValidationIntegration:
    """Test metadata validation integration."""
    
    @patch('langflow.services.database.config.get_database_service')
    async def test_metadata_completeness_validation(
        self, mock_get_db_service, mock_database_service, sample_book_metadata
    ):
        """Test comprehensive metadata validation."""
        mock_get_db_service.return_value = mock_database_service
        mock_database_service.get_document.return_value = {
            "id": sample_book_metadata.id,
            **sample_book_metadata.dict()
        }
        
        metadata_service = MetadataManagementService()
        
        # Test complete metadata
        result = await metadata_service.validate_metadata_completeness(
            sample_book_metadata.id,
            "test_workspace_123"
        )
        
        assert result["completeness_score"] >= 90
        assert result["is_complete"] is True
        assert len(result["missing_fields"]) == 0
    
    @patch('langflow.services.database.config.get_database_service') 
    async def test_isbn_validation_integration(
        self, mock_get_db_service, mock_database_service
    ):
        """Test ISBN validation integration."""
        mock_get_db_service.return_value = mock_database_service
        
        metadata_service = MetadataManagementService()
        
        # Test valid ISBN-13
        result = await metadata_service.validate_isbn("978-0-123456-78-9")
        assert result["valid"] is True
        assert result["format"] == "ISBN-13"
        
        # Test invalid ISBN
        result = await metadata_service.validate_isbn("invalid-isbn")
        assert result["valid"] is False
        assert "Invalid ISBN format" in result["error"]
    
    async def test_category_optimization_integration(self):
        """Test category and keyword optimization."""
        metadata_service = MetadataManagementService()
        
        # Test category suggestion
        book_description = "A thrilling science fiction adventure set in the distant future with space travel and alien encounters."
        
        with patch.object(metadata_service, 'analyze_content_for_categories') as mock_analyze:
            mock_analyze.return_value = {
                "suggested_categories": [
                    "Fiction > Science Fiction > Space Opera",
                    "Fiction > Science Fiction > Adventure",
                    "Fiction > Adventure > Science Fiction"
                ],
                "confidence_scores": [0.95, 0.87, 0.78],
                "suggested_keywords": ["space", "future", "alien", "adventure", "scifi", "travel", "exploration"]
            }
            
            result = await metadata_service.optimize_categories_and_keywords(
                book_description,
                "Integration Test Novel",
                "test_workspace_123"
            )
            
            assert len(result["suggested_categories"]) >= 2
            assert len(result["suggested_keywords"]) <= 7  # KDP limit
            assert all(score >= 0.7 for score in result["confidence_scores"])


class TestPricingIntegration:
    """Test pricing calculation integration."""
    
    async def test_pricing_strategy_calculation(self, sample_publishing_project):
        """Test comprehensive pricing strategy calculation."""
        from langflow.services.publishing.pricing_calculator import PricingCalculator
        
        pricing_service = PricingCalculator()
        
        # Mock market data
        market_data = {
            "category": "Fiction > Science Fiction",
            "print_prices": [9.99, 12.99, 14.99, 16.99, 19.99],
            "ebook_prices": [2.99, 3.99, 4.99, 6.99, 7.99],
            "competitor_count": 25,
            "avg_page_count": 280
        }
        
        with patch.object(pricing_service, 'calculate_print_cost') as mock_print_cost, \
             patch.object(pricing_service, 'analyze_competitor_pricing') as mock_competitor_analysis:
            
            mock_print_cost.return_value = 4.50
            mock_competitor_analysis.return_value = {
                "print": {
                    "median": 14.99,
                    "75th_percentile": 16.99,
                    "average": 14.78
                },
                "ebook": {
                    "median": 4.99,
                    "75th_percentile": 6.99,
                    "average": 5.18
                }
            }
            
            result = await pricing_service.calculate_pricing_strategy(
                sample_publishing_project.id,
                market_data,
                "test_workspace_123"
            )
            
            assert "print" in result
            assert "ebook" in result
            assert result["print"]["minimum_price"] > 4.50  # Above print cost
            assert result["ebook"]["minimum_price"] >= 0.99  # KDP minimum
            assert "profit_margins" in result["print"]
            assert "royalty_options" in result["ebook"]


class TestWorkflowPerformanceIntegration:
    """Test workflow performance and optimization."""
    
    @pytest.mark.performance
    async def test_concurrent_workflow_operations(self, mock_database_service):
        """Test concurrent workflow operations performance."""
        workflow_service = PublishingWorkflowService()
        
        # Mock multiple project operations
        mock_database_service.create_document.return_value = {"id": "test_project"}
        mock_database_service.get_document.return_value = {"id": "test_project", "status": "draft"}
        
        with patch.object(workflow_service, 'database_service', mock_database_service):
            # Test concurrent project initializations
            tasks = []
            for i in range(10):
                task = workflow_service.initialize_publishing_project(
                    f"book_{i}",
                    ["print"],
                    "test_workspace_123"
                )
                tasks.append(task)
            
            import time
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()
            
            # Should complete quickly with concurrent operations
            assert end_time - start_time < 2.0
            assert len(results) == 10
            assert all(isinstance(r, dict) and r.get("success") for r in results if not isinstance(r, Exception))
    
    @pytest.mark.performance
    async def test_workflow_memory_efficiency(self, mock_database_service):
        """Test workflow memory efficiency with large datasets."""
        workflow_service = PublishingWorkflowService()
        
        # Mock large dataset
        large_projects = []
        for i in range(100):
            large_projects.append({
                "id": f"project_{i}",
                "book_id": f"book_{i}",
                "status": "draft",
                "word_count": 50000 + (i * 1000)
            })
        
        mock_database_service.query_documents.return_value = large_projects
        
        with patch.object(workflow_service, 'database_service', mock_database_service):
            # Test processing large number of projects
            result = await workflow_service.get_workspace_publishing_projects("test_workspace_123")
            
            assert len(result["projects"]) == 100
            # Memory usage should be reasonable (this is a basic check in test environment)
            import sys
            assert sys.getsizeof(result) < 10 * 1024 * 1024  # Less than 10MB


class TestErrorRecoveryIntegration:
    """Test error recovery and rollback mechanisms."""
    
    @patch('langflow.services.database.config.get_database_service')
    async def test_publishing_failure_rollback(
        self, mock_get_db_service, mock_database_service, sample_publishing_project
    ):
        """Test rollback on publishing failure."""
        mock_get_db_service.return_value = mock_database_service
        
        # Mock initial success then failure
        mock_database_service.get_document.return_value = {
            "id": sample_publishing_project.id,
            "status": "ready_to_publish",
            **sample_publishing_project.dict()
        }
        
        workflow_service = PublishingWorkflowService()
        kdp_service = AmazonKDPService()
        
        # Mock KDP failure
        with patch.object(kdp_service, 'publish_book') as mock_publish:
            mock_publish.side_effect = HTTPException(status_code=503, detail="KDP service unavailable")
            
            with patch.object(workflow_service, 'database_service', mock_database_service):
                with pytest.raises(HTTPException) as exc_info:
                    await workflow_service.execute_publishing_workflow(
                        sample_publishing_project.id,
                        "test_workspace_123"
                    )
                
                assert exc_info.value.status_code == 503
                # Verify rollback was attempted
                mock_database_service.update_document.assert_called()
    
    async def test_network_failure_retry_mechanism(self):
        """Test retry mechanism on network failures."""
        kdp_service = AmazonKDPService()
        
        # Mock network failure followed by success
        call_count = 0
        
        async def mock_request(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:  # Fail first 2 attempts
                raise aiohttp.ClientError("Network timeout")
            else:  # Succeed on 3rd attempt
                mock_response = AsyncMock()
                mock_response.status = 200
                mock_response.json = AsyncMock(return_value={"success": True})
                return mock_response
        
        kdp_service.session = AsyncMock()
        kdp_service.session.request = mock_request
        
        # Should succeed after retries
        with patch('asyncio.sleep'):  # Speed up test
            response = await kdp_service.make_kdp_request("GET", "/test", {})
            
        assert response.status == 200
        assert call_count == 3  # 2 failures + 1 success


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])