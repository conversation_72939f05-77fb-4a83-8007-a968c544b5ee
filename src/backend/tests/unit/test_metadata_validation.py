"""
Unit tests for metadata validation and management.

This test suite covers:
- ISBN validation and format checking
- Category and keyword optimization
- Metadata completeness scoring
- KDP compliance validation
- Author and contributor management
- Rights and copyright validation
- Database provider compatibility (Firebase primary, SQL fallback)
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch
from datetime import datetime, date
import re

from langflow.services.publishing.metadata_management_service import MetadataManagementService
from langflow.services.publishing.isbn_manager import ISBNManager
from langflow.services.publishing.category_keyword_optimizer import CategoryKeywordOptimizer
from langflow.services.publishing.marketing_copy_manager import MarketingCopyManager
from langflow.services.publishing.rights_copyright_manager import RightsCopyrightManager
from langflow.schema.publishing import BookMetadata
from langflow.services.deps import get_database_service


@pytest.fixture
def metadata_service():
    """Create metadata management service for testing."""
    return MetadataManagementService()


@pytest.fixture
def isbn_manager():
    """Create ISBN manager for testing."""
    return ISBNManager()


@pytest.fixture
def category_optimizer():
    """Create category keyword optimizer for testing."""
    return CategoryKeywordOptimizer()


@pytest.fixture
def marketing_copy_manager():
    """Create marketing copy manager for testing."""
    return MarketingCopyManager()


@pytest.fixture
def rights_manager():
    """Create rights copyright manager for testing."""
    return RightsCopyrightManager()


@pytest.fixture
def sample_book_metadata():
    """Create comprehensive sample book metadata."""
    return BookMetadata(
        id="test_metadata_123",
        publishing_project_id="test_project_456",
        title="The Complete Test Novel",
        subtitle="A Comprehensive Story for Validation Testing",
        series_title="Test Series",
        volume_number=1,
        primary_author="Test Author",
        co_authors=["Co-Author One", "Co-Author Two"],
        author_bio="Test Author is an experienced writer with multiple published works in science fiction and fantasy.",
        contributor_bios=[
            {"name": "Co-Author One", "bio": "Specialist in character development and dialogue."},
            {"name": "Co-Author Two", "bio": "Expert in world-building and plot structure."}
        ],
        description="A comprehensive test novel designed to validate all aspects of the metadata management system. This story follows characters through various adventures while testing every validation rule.",
        marketing_copy="An epic adventure that will captivate readers from the first page to the last!",
        back_cover_copy="What readers are saying: 'Amazing!' - Test Reviewer",
        primary_category="Fiction > Science Fiction > Space Opera",
        secondary_category="Fiction > Adventure > Science Fiction",
        keywords=["space", "adventure", "scifi", "future", "aliens", "technology", "exploration"],
        age_range="Young Adult",
        grade_range="9-12",
        language="English",
        publication_country="US",
        copyright_year=2024,
        copyright_holder="Test Author",
        publisher="Test Publishing House",
        imprint="Test Imprint",
        content_warnings=["mild language", "sci-fi violence"],
        maturity_rating="T (Teen)",
        workspace_id="test_workspace_789"
    )


@pytest.fixture
def incomplete_metadata():
    """Create incomplete metadata for testing validation."""
    return BookMetadata(
        title="Incomplete Test Novel",
        primary_author="Test Author",
        description="A short description.",
        primary_category="Fiction",
        language="English",
        copyright_year=2024,
        copyright_holder="Test Author",
        workspace_id="test_workspace_789"
    )


class TestISBNValidation:
    """Test ISBN validation and management."""
    
    def test_validate_isbn_13_valid(self, isbn_manager):
        """Test validation of valid ISBN-13."""
        valid_isbn_13 = "978-0-596-52068-7"
        
        result = isbn_manager.validate_isbn(valid_isbn_13)
        
        assert result["valid"] is True
        assert result["format"] == "ISBN-13"
        assert result["formatted"] == "9780596520687"
        assert "checksum_valid" in result
        assert result["checksum_valid"] is True
    
    def test_validate_isbn_10_valid(self, isbn_manager):
        """Test validation of valid ISBN-10."""
        valid_isbn_10 = "0-596-52068-9"
        
        result = isbn_manager.validate_isbn(valid_isbn_10)
        
        assert result["valid"] is True
        assert result["format"] == "ISBN-10"
        assert result["formatted"] == "0596520689"
        assert result["checksum_valid"] is True
    
    def test_validate_isbn_invalid_format(self, isbn_manager):
        """Test validation of invalid ISBN format."""
        invalid_isbns = [
            "123-456-789",      # Too short
            "978-123-456-789-0-1",  # Too long
            "invalid-isbn",     # Non-numeric
            "",                 # Empty
            "978-0-596-52068-8" # Wrong checksum
        ]
        
        for isbn in invalid_isbns:
            result = isbn_manager.validate_isbn(isbn)
            assert result["valid"] is False
            assert "error" in result
    
    def test_isbn_checksum_calculation(self, isbn_manager):
        """Test ISBN checksum calculation."""
        # Test ISBN-13 checksum
        isbn_13_digits = "978059652068"
        checksum = isbn_manager.calculate_isbn_13_checksum(isbn_13_digits)
        assert checksum == "7"
        
        # Test ISBN-10 checksum
        isbn_10_digits = "059652068"
        checksum = isbn_manager.calculate_isbn_10_checksum(isbn_10_digits)
        assert checksum == "9"
    
    def test_isbn_format_conversion(self, isbn_manager):
        """Test ISBN format conversion between ISBN-10 and ISBN-13."""
        # Convert ISBN-10 to ISBN-13
        isbn_10 = "0596520689"
        result = isbn_manager.convert_isbn_10_to_13(isbn_10)
        
        assert result["success"] is True
        assert result["isbn_13"].startswith("978")
        assert len(result["isbn_13"]) == 13
        
        # Convert ISBN-13 to ISBN-10 (if possible)
        isbn_13 = "9780596520687"
        result = isbn_manager.convert_isbn_13_to_10(isbn_13)
        
        if result["convertible"]:
            assert result["success"] is True
            assert len(result["isbn_10"]) == 10
        else:
            assert "reason" in result
    
    def test_isbn_agency_lookup(self, isbn_manager):
        """Test ISBN agency and publisher lookup."""
        isbn = "978-0-596-52068-7"
        
        with patch.object(isbn_manager, 'lookup_isbn_agency') as mock_lookup:
            mock_lookup.return_value = {
                "agency": "Bowker (US)",
                "country": "United States",
                "publisher_prefix": "0-596",
                "publisher_name": "O'Reilly Media"
            }
            
            result = isbn_manager.lookup_isbn_agency(isbn)
            
            assert result["agency"] == "Bowker (US)"
            assert result["country"] == "United States"
            assert result["publisher_prefix"] == "0-596"
    
    def test_isbn_format_assignment(self, isbn_manager):
        """Test ISBN assignment for different formats."""
        publishing_project_data = {
            "print_enabled": True,
            "ebook_enabled": True,
            "hardcover_enabled": True
        }
        
        result = isbn_manager.assign_isbns_for_formats(publishing_project_data)
        
        assert "isbn_assignments" in result
        assignments = result["isbn_assignments"]
        
        if publishing_project_data["print_enabled"]:
            assert "print" in assignments
        if publishing_project_data["ebook_enabled"]:
            assert "ebook" in assignments
        if publishing_project_data["hardcover_enabled"]:
            assert "hardcover" in assignments


class TestCategoryKeywordOptimization:
    """Test category and keyword optimization."""
    
    def test_analyze_content_for_categories(self, category_optimizer):
        """Test content analysis for category suggestions."""
        book_content = {
            "title": "Starship Adventures",
            "description": "An epic space opera featuring interstellar travel, alien civilizations, and advanced technology. The story follows a crew of explorers as they discover new worlds and face cosmic threats.",
            "genre_tags": ["science fiction", "space opera", "adventure"]
        }
        
        with patch.object(category_optimizer, 'ai_content_analyzer') as mock_analyzer:
            mock_analyzer.return_value = {
                "suggested_categories": [
                    "Fiction > Science Fiction > Space Opera",
                    "Fiction > Science Fiction > Adventure",
                    "Fiction > Adventure > Science Fiction"
                ],
                "confidence_scores": [0.95, 0.87, 0.73],
                "reasoning": ["Strong space opera elements", "Adventure themes present", "Sci-fi setting"]
            }
            
            result = category_optimizer.analyze_content_for_categories(book_content)
            
            assert len(result["suggested_categories"]) >= 2
            assert all(score >= 0.7 for score in result["confidence_scores"])
            assert "Fiction > Science Fiction" in result["suggested_categories"][0]
    
    def test_kdp_category_validation(self, category_optimizer):
        """Test KDP category validation."""
        # Valid KDP categories
        valid_categories = [
            "Fiction > Science Fiction > Space Opera",
            "Fiction > Romance > Contemporary",
            "Non-Fiction > Business > Entrepreneurship",
            "Children's Books > Science Fiction & Fantasy"
        ]
        
        for category in valid_categories:
            result = category_optimizer.validate_kdp_category(category)
            assert result["valid"] is True
            assert "bisac_code" in result
        
        # Invalid categories
        invalid_categories = [
            "Fiction > Invalid Category",
            "Custom Category",
            ""
        ]
        
        for category in invalid_categories:
            result = category_optimizer.validate_kdp_category(category)
            assert result["valid"] is False
            assert "error" in result
    
    def test_keyword_optimization(self, category_optimizer):
        """Test keyword optimization for KDP."""
        book_data = {
            "title": "The Space Explorer",
            "description": "A thrilling adventure through space with aliens and technology",
            "category": "Fiction > Science Fiction > Space Opera"
        }
        
        result = category_optimizer.optimize_keywords(book_data)
        
        assert "optimized_keywords" in result
        assert "keyword_scores" in result
        assert "suggestions" in result
        
        keywords = result["optimized_keywords"]
        assert len(keywords) <= 7  # KDP limit
        assert all(len(keyword) <= 50 for keyword in keywords)  # Length limit
        
        # Should include relevant terms
        keyword_text = " ".join(keywords).lower()
        assert any(term in keyword_text for term in ["space", "science", "fiction", "adventure"])
    
    def test_keyword_performance_analysis(self, category_optimizer):
        """Test keyword performance analysis."""
        keywords = ["space opera", "science fiction", "adventure", "aliens", "technology"]
        
        with patch.object(category_optimizer, 'analyze_keyword_performance') as mock_analysis:
            mock_analysis.return_value = {
                "keyword_metrics": {
                    "space opera": {"search_volume": 5400, "competition": "medium", "relevance": 0.92},
                    "science fiction": {"search_volume": 12000, "competition": "high", "relevance": 0.88},
                    "adventure": {"search_volume": 8900, "competition": "high", "relevance": 0.75},
                    "aliens": {"search_volume": 3200, "competition": "low", "relevance": 0.85},
                    "technology": {"search_volume": 15000, "competition": "very high", "relevance": 0.65}
                },
                "recommendations": [
                    "Consider 'space opera' for high relevance and medium competition",
                    "Replace 'technology' with more specific terms"
                ]
            }
            
            result = category_optimizer.analyze_keyword_performance(keywords)
            
            assert "keyword_metrics" in result
            assert "recommendations" in result
            
            # Check metrics structure
            for keyword in keywords:
                metrics = result["keyword_metrics"][keyword]
                assert "search_volume" in metrics
                assert "competition" in metrics
                assert "relevance" in metrics
                assert 0 <= metrics["relevance"] <= 1
    
    def test_category_market_analysis(self, category_optimizer):
        """Test category market analysis."""
        category = "Fiction > Science Fiction > Space Opera"
        
        with patch.object(category_optimizer, 'analyze_category_market') as mock_analysis:
            mock_analysis.return_value = {
                "market_size": "medium",
                "competition_level": "moderate",
                "top_performers": [
                    {"title": "Top Space Opera", "rank": 1, "price": 14.99},
                    {"title": "Popular Sci-Fi", "rank": 5, "price": 12.99}
                ],
                "pricing_insights": {
                    "average_price": 13.49,
                    "price_range": {"min": 9.99, "max": 19.99},
                    "sweet_spot": 12.99
                },
                "trend_analysis": {
                    "growth_trend": "stable",
                    "seasonal_patterns": ["summer peak", "holiday boost"]
                }
            }
            
            result = category_optimizer.analyze_category_market(category)
            
            assert "market_size" in result
            assert "competition_level" in result
            assert "pricing_insights" in result
            assert "trend_analysis" in result


class TestMetadataCompletenessValidation:
    """Test metadata completeness and quality validation."""
    
    async def test_complete_metadata_validation(self, metadata_service, sample_book_metadata):
        """Test validation of complete metadata."""
        with patch.object(metadata_service, 'database_service') as mock_db:
            mock_db.get_document.return_value = sample_book_metadata.dict()
            
            result = await metadata_service.validate_metadata_completeness(
                sample_book_metadata.id,
                "test_workspace_789"
            )
            
            assert result["completeness_score"] >= 90
            assert result["is_complete"] is True
            assert len(result["missing_fields"]) == 0
            assert "validation_details" in result
    
    async def test_incomplete_metadata_validation(self, metadata_service, incomplete_metadata):
        """Test validation of incomplete metadata."""
        with patch.object(metadata_service, 'database_service') as mock_db:
            mock_db.get_document.return_value = incomplete_metadata.dict()
            
            result = await metadata_service.validate_metadata_completeness(
                incomplete_metadata.id,
                "test_workspace_789"
            )
            
            assert result["completeness_score"] < 90
            assert result["is_complete"] is False
            assert len(result["missing_fields"]) > 0
            assert "recommendations" in result
    
    def test_kdp_compliance_validation(self, metadata_service, sample_book_metadata):
        """Test KDP compliance validation."""
        result = metadata_service.validate_kdp_compliance(sample_book_metadata)
        
        assert "compliant" in result
        assert "violations" in result
        assert "warnings" in result
        
        # Check specific KDP requirements
        assert result["title_length_valid"] is True  # Title should be under 255 chars
        assert result["description_length_valid"] is True  # Description should be under 4000 chars
        assert result["keyword_count_valid"] is True  # Keywords should be 7 or fewer
        
        if result["violations"]:
            assert all("requirement" in violation for violation in result["violations"])
    
    def test_metadata_quality_scoring(self, metadata_service, sample_book_metadata):
        """Test metadata quality scoring algorithm."""
        result = metadata_service.calculate_metadata_quality_score(sample_book_metadata)
        
        assert "overall_score" in result
        assert "category_scores" in result
        assert "improvement_suggestions" in result
        
        # Check score range
        assert 0 <= result["overall_score"] <= 100
        
        # Check category scores
        categories = result["category_scores"]
        expected_categories = ["basic_info", "description_quality", "categorization", "author_info", "rights_info"]
        
        for category in expected_categories:
            if category in categories:
                assert 0 <= categories[category] <= 100
    
    def test_description_quality_analysis(self, metadata_service):
        """Test book description quality analysis."""
        # High quality description
        good_description = """
        In a galaxy far from Earth, Captain Sarah Chen discovers an ancient alien artifact that could change the course of human history. When hostile forces threaten to claim the technology for themselves, Sarah must assemble a diverse crew of specialists to protect this discovery.
        
        Racing across star systems and battling both external enemies and internal conflicts, the crew faces impossible choices that will determine the fate of two civilizations. This epic space opera combines thrilling action, complex characters, and thought-provoking themes about humanity's place in the universe.
        
        Perfect for fans of science fiction adventure and character-driven storytelling.
        """
        
        result = metadata_service.analyze_description_quality(good_description)
        
        assert result["quality_score"] >= 75
        assert "readability_score" in result
        assert "engagement_factors" in result
        assert "improvement_suggestions" in result
        
        # Poor quality description
        poor_description = "This is a book about space. It has aliens. You should read it."
        
        result = metadata_service.analyze_description_quality(poor_description)
        
        assert result["quality_score"] < 50
        assert len(result["improvement_suggestions"]) > 0
    
    def test_title_optimization_analysis(self, metadata_service):
        """Test book title optimization analysis."""
        titles_to_test = [
            {"title": "The Space Explorer", "subtitle": "A Journey to Unknown Worlds"},
            {"title": "Love in the Time of Algorithms", "subtitle": None},
            {"title": "A Very Very Very Long Title That Might Be Too Long For Optimal Performance", "subtitle": "And a Subtitle Too"}
        ]
        
        for title_data in titles_to_test:
            result = metadata_service.analyze_title_optimization(
                title_data["title"],
                title_data["subtitle"]
            )
            
            assert "optimization_score" in result
            assert "length_analysis" in result
            assert "keyword_analysis" in result
            assert "suggestions" in result
            
            # Check length analysis
            length_analysis = result["length_analysis"]
            assert "title_length" in length_analysis
            assert "optimal_range" in length_analysis


class TestMarketingCopyManagement:
    """Test marketing copy generation and management."""
    
    def test_generate_marketing_copy(self, marketing_copy_manager, sample_book_metadata):
        """Test marketing copy generation."""
        with patch.object(marketing_copy_manager, 'ai_copy_generator') as mock_generator:
            mock_generator.return_value = {
                "marketing_copy": "An epic adventure that will captivate readers from start to finish!",
                "back_cover_copy": "What critics are saying: 'Absolutely brilliant!' - Review Magazine",
                "social_media_copy": "🚀 New release! Epic space adventure awaits. #SciFi #SpaceOpera #NewRelease",
                "elevator_pitch": "A thrilling space opera about discovery, courage, and the future of humanity."
            }
            
            result = marketing_copy_manager.generate_marketing_copy(sample_book_metadata)
            
            assert "marketing_copy" in result
            assert "back_cover_copy" in result
            assert "social_media_copy" in result
            assert "elevator_pitch" in result
            
            # Validate copy quality
            assert len(result["marketing_copy"]) > 20
            assert len(result["back_cover_copy"]) > 20
    
    def test_copy_analysis_and_optimization(self, marketing_copy_manager):
        """Test marketing copy analysis and optimization."""
        marketing_copy = "This book is good. You should read it. It has characters and a plot."
        
        result = marketing_copy_manager.analyze_copy_effectiveness(marketing_copy)
        
        assert "effectiveness_score" in result
        assert "issues" in result
        assert "suggestions" in result
        
        # Should identify issues with weak copy
        assert result["effectiveness_score"] < 60
        assert len(result["suggestions"]) > 0
        
        # Test optimization
        optimization_result = marketing_copy_manager.optimize_marketing_copy(marketing_copy)
        
        assert "optimized_copy" in optimization_result
        assert "improvements_made" in optimization_result
        assert len(optimization_result["optimized_copy"]) > len(marketing_copy)
    
    def test_copy_template_system(self, marketing_copy_manager):
        """Test marketing copy template system."""
        book_data = {
            "genre": "Science Fiction",
            "target_audience": "Young Adult",
            "key_themes": ["adventure", "discovery", "friendship"],
            "tone": "exciting"
        }
        
        result = marketing_copy_manager.generate_from_template(book_data)
        
        assert "template_copy" in result
        assert "template_used" in result
        assert "customization_notes" in result
        
        # Verify template variables were replaced
        copy = result["template_copy"]
        assert "Science Fiction" in copy or "sci-fi" in copy.lower()
        assert any(theme in copy.lower() for theme in book_data["key_themes"])


class TestRightsCopyrightManagement:
    """Test rights and copyright management."""
    
    def test_copyright_validation(self, rights_manager, sample_book_metadata):
        """Test copyright information validation."""
        result = rights_manager.validate_copyright_info(sample_book_metadata)
        
        assert "valid" in result
        assert "issues" in result
        assert "recommendations" in result
        
        # Check specific validations
        assert result["copyright_year_valid"] is True
        assert result["copyright_holder_valid"] is True
        
        if result["issues"]:
            assert all("type" in issue for issue in result["issues"])
    
    def test_rights_tracking(self, rights_manager):
        """Test publishing rights tracking."""
        rights_data = {
            "territories": ["US", "Canada", "UK"],
            "languages": ["English"],
            "formats": ["print", "ebook", "audiobook"],
            "duration": "lifetime",
            "exclusive": True,
            "reversion_clause": True
        }
        
        result = rights_manager.track_publishing_rights(rights_data)
        
        assert "rights_summary" in result
        assert "territory_coverage" in result
        assert "format_coverage" in result
        assert "compliance_status" in result
    
    def test_plagiarism_detection(self, rights_manager):
        """Test plagiarism detection capabilities."""
        content_to_check = """
        This is a sample text that should be checked for potential plagiarism issues.
        It contains various sentences and phrases that might match existing content.
        """
        
        with patch.object(rights_manager, 'plagiarism_checker') as mock_checker:
            mock_checker.return_value = {
                "plagiarism_score": 15,  # Low plagiarism percentage
                "matches_found": [
                    {"text": "sample text", "source": "unknown", "confidence": 0.3}
                ],
                "risk_level": "low",
                "recommendations": ["Review flagged content"]
            }
            
            result = rights_manager.check_plagiarism(content_to_check)
            
            assert "plagiarism_score" in result
            assert "matches_found" in result
            assert "risk_level" in result
            assert result["plagiarism_score"] < 30  # Should be low
    
    def test_legal_compliance_check(self, rights_manager, sample_book_metadata):
        """Test legal compliance checking."""
        result = rights_manager.check_legal_compliance(sample_book_metadata)
        
        assert "compliant" in result
        assert "issues" in result
        assert "required_disclaimers" in result
        
        # Check specific compliance areas
        assert "copyright_compliant" in result
        assert "content_warning_adequate" in result
        assert "attribution_complete" in result


class TestDatabaseCompatibilityMetadata:
    """Test metadata service compatibility with different database providers."""
    
    @pytest.mark.parametrize("database_provider", ["firebase", "sqlite"])
    async def test_metadata_crud_operations(self, database_provider, sample_book_metadata):
        """Test metadata CRUD operations with different database providers."""
        # Mock database service based on provider
        if database_provider == "firebase":
            mock_db_service = AsyncMock()
            mock_db_service.create_document.return_value = {"id": sample_book_metadata.id}
            mock_db_service.get_document.return_value = sample_book_metadata.dict()
            mock_db_service.update_document.return_value = {"success": True}
        else:
            mock_db_service = AsyncMock() 
            mock_db_service.add.return_value = None
            mock_db_service.commit.return_value = None
            mock_db_service.get.return_value = sample_book_metadata
        
        metadata_service = MetadataManagementService()
        
        with patch.object(metadata_service, 'database_service', mock_db_service):
            # Test create operation
            create_result = await metadata_service.create_book_metadata(
                "test_project_123",
                sample_book_metadata.dict(),
                "test_workspace_789"
            )
            
            assert create_result["success"] is True
            
            # Test read operation
            read_result = await metadata_service.get_book_metadata(
                sample_book_metadata.id,
                "test_workspace_789"
            )
            
            assert read_result["success"] is True
            assert read_result["metadata"]["title"] == sample_book_metadata.title
            
            # Test update operation
            updates = {"description": "Updated description for testing"}
            update_result = await metadata_service.update_book_metadata(
                sample_book_metadata.id,
                updates,
                "test_workspace_789"
            )
            
            assert update_result["success"] is True
    
    @pytest.mark.parametrize("database_provider", ["firebase", "sqlite"])
    async def test_metadata_query_operations(self, database_provider):
        """Test metadata query operations with different providers."""
        # Mock query results
        mock_metadata_list = [
            {"id": "meta_1", "title": "Book One", "primary_category": "Fiction"},
            {"id": "meta_2", "title": "Book Two", "primary_category": "Non-Fiction"},
            {"id": "meta_3", "title": "Book Three", "primary_category": "Fiction"}
        ]
        
        if database_provider == "firebase":
            mock_db_service = AsyncMock()
            mock_db_service.query_documents.return_value = mock_metadata_list
        else:
            mock_db_service = AsyncMock()
            mock_db_service.query.return_value.filter.return_value.all.return_value = [
                Mock(**metadata) for metadata in mock_metadata_list
            ]
        
        metadata_service = MetadataManagementService()
        
        with patch.object(metadata_service, 'database_service', mock_db_service):
            # Test query by category
            result = await metadata_service.query_metadata_by_category(
                "Fiction",
                "test_workspace_789"
            )
            
            assert "metadata_items" in result
            assert len(result["metadata_items"]) >= 2  # Should find Fiction books


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])