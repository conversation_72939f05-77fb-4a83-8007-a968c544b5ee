"""
Unit tests for manuscript formatting quality validation.

This test suite covers:
- Typography quality analysis and scoring
- Print formatting validation for KDP compliance
- EPUB/MOBI generation quality checks
- Font consistency and layout validation
- Performance testing with large manuscripts
- Database provider compatibility (Firebase primary, SQL fallback)
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch, mock_open
from datetime import datetime
import tempfile
import json
from pathlib import Path
from io import BytesIO
import os

from langflow.services.publishing.manuscript_formatting_service import ManuscriptFormattingService
from langflow.services.publishing.typography_engine import TypographyEngine
from langflow.services.publishing.print_formatting_engine import PrintFormattingEngine
from langflow.services.publishing.ebook_generator import EbookGenerator
from langflow.schema.publishing import ManuscriptFormat, PublishingProject
from langflow.services.database.models.book.model import Book


@pytest.fixture
def typography_engine():
    """Create typography engine for testing."""
    return TypographyEngine()


@pytest.fixture
def print_formatting_engine():
    """Create print formatting engine for testing."""
    return PrintFormattingEngine()


@pytest.fixture
def ebook_generator():
    """Create ebook generator for testing."""
    return EbookGenerator()


@pytest.fixture
def manuscript_formatting_service():
    """Create manuscript formatting service for testing."""
    return ManuscriptFormattingService()


@pytest.fixture
def sample_manuscript_content():
    """Create sample manuscript content for testing."""
    return {
        "title_page": {
            "title": "Test Novel",
            "subtitle": "A Quality Testing Story",
            "author": "Test Author",
            "publisher": "Test Publishing"
        },
        "copyright_page": {
            "copyright_year": 2024,
            "copyright_holder": "Test Author",
            "isbn": "978-0-123456-78-9",
            "publisher": "Test Publishing"
        },
        "table_of_contents": [
            {"title": "Chapter 1: The Beginning", "page": 1},
            {"title": "Chapter 2: The Middle", "page": 15},
            {"title": "Chapter 3: The End", "page": 28}
        ],
        "chapters": [
            {
                "title": "Chapter 1: The Beginning",
                "number": 1,
                "content": """This is the first chapter of our test novel. It contains various formatting elements that need to be validated for quality.

"This is dialogue," said the character. "It should be properly formatted with smart quotes."

This paragraph contains em-dashes—like this one—and en-dashes for ranges like 1990–1995. It also has ellipses... and proper punctuation.

This chapter has multiple paragraphs with proper spacing. Each paragraph should maintain consistent formatting and typography throughout the document."""
            },
            {
                "title": "Chapter 2: The Middle", 
                "number": 2,
                "content": """The second chapter continues the story with consistent formatting. It includes italics for *emphasis* and bold text for **strong emphasis**.

This chapter tests various punctuation marks: semicolons; colons: question marks? exclamation points! And proper apostrophes in contractions like don't, won't, and can't.

Numbered lists should be formatted properly:
1. First item
2. Second item  
3. Third item

And bulleted lists too:
• First bullet
• Second bullet
• Third bullet"""
            },
            {
                "title": "Chapter 3: The End",
                "number": 3, 
                "content": """The final chapter wraps up our test novel. It includes special characters like ©, ®, and ™ symbols.

This chapter also tests spacing consistency, paragraph alignment, and proper page breaks. The formatting should remain consistent throughout the entire manuscript.

"The end," concluded the narrator, "should be properly formatted with consistent typography and layout."""
            }
        ]
    }


@pytest.fixture
def sample_manuscript_format():
    """Create sample manuscript format specifications."""
    return ManuscriptFormat(
        id="test_format_123",
        publishing_project_id="test_project_456",
        format_type="print",
        trim_size="6x9",
        paper_type="white",
        binding_type="paperback",
        margin_top=0.75,
        margin_bottom=0.75,
        margin_inside=0.75,
        margin_outside=0.75,
        font_family="Times New Roman",
        font_size=11,
        line_spacing=1.15,
        paragraph_spacing=0.0,
        chapter_start_new_page=True,
        chapter_number_style="Chapter 1",
        page_numbering="bottom_center",
        workspace_id="test_workspace_789"
    )


class TestTypographyQuality:
    """Test typography quality analysis and validation."""
    
    def test_analyze_typography_quality(self, typography_engine, sample_manuscript_content):
        """Test comprehensive typography quality analysis."""
        content = sample_manuscript_content["chapters"][0]["content"]
        
        result = typography_engine.analyze_typography_quality(content)
        
        assert "overall_score" in result
        assert "issues" in result
        assert "recommendations" in result
        assert isinstance(result["overall_score"], (int, float))
        assert 0 <= result["overall_score"] <= 100
        assert isinstance(result["issues"], list)
        assert isinstance(result["recommendations"], list)
    
    def test_quote_consistency_validation(self, typography_engine):
        """Test quote consistency validation."""
        # Text with inconsistent quotes
        text_with_issues = '''He said "This uses straight quotes" and then 'this uses different quotes' and finally "this mixes them.'''
        
        result = typography_engine.validate_quote_consistency(text_with_issues)
        
        assert "consistent" in result
        assert result["consistent"] is False
        assert "issues" in result
        assert len(result["issues"]) > 0
        
        # Text with consistent quotes
        text_consistent = '''He said "This uses proper quotes" and then "this also uses proper quotes."'''
        
        result = typography_engine.validate_quote_consistency(text_consistent)
        
        assert result["consistent"] is True
        assert len(result["issues"]) == 0
    
    def test_punctuation_validation(self, typography_engine):
        """Test punctuation consistency validation."""
        # Text with various punctuation issues
        text_with_issues = """This has spacing issues...like this.And missing spaces.Also  double  spaces.
        
        This has-- wrong dashes-and mixed punctuation."""
        
        result = typography_engine.validate_punctuation(text_with_issues)
        
        assert "score" in result
        assert "issues" in result
        assert isinstance(result["score"], (int, float))
        assert 0 <= result["score"] <= 100
        
        # Check for specific issue types
        issue_types = [issue["type"] for issue in result["issues"]]
        assert "spacing" in issue_types or "punctuation" in issue_types
    
    def test_spacing_consistency_validation(self, typography_engine):
        """Test spacing consistency validation."""
        # Text with spacing issues
        text_with_issues = """This paragraph has  double spaces.
        
        
        
        This has too many line breaks.
        And this  has   irregular    spacing."""
        
        result = typography_engine.validate_spacing_consistency(text_with_issues)
        
        assert "consistent" in result
        assert "issues" in result
        assert isinstance(result["consistent"], bool)
        
        if not result["consistent"]:
            assert len(result["issues"]) > 0
    
    def test_paragraph_formatting_validation(self, typography_engine):
        """Test paragraph formatting validation."""
        # Text with paragraph formatting issues
        text_with_issues = """This is a paragraph.
But this line doesn't start properly.
        This line has wrong indentation.
        
        This paragraph has  inconsistent   spacing."""
        
        result = typography_engine.validate_paragraph_formatting(text_with_issues)
        
        assert "score" in result
        assert "issues" in result
        assert "recommendations" in result
        assert isinstance(result["score"], (int, float))
    
    def test_dialogue_formatting_validation(self, typography_engine):
        """Test dialogue formatting validation."""
        # Text with dialogue formatting issues
        text_with_issues = '''He said "This is dialogue but missing proper punctuation"
        'This uses single quotes,' she replied.
        "And this," he continued "has missing comma."'''
        
        result = typography_engine.validate_dialogue_formatting(text_with_issues)
        
        assert "score" in result
        assert "issues" in result
        assert isinstance(result["score"], (int, float))
        
        # Check for dialogue-specific issues
        if result["issues"]:
            issue_types = [issue["type"] for issue in result["issues"]]
            assert any(t in ["quote_style", "punctuation", "dialogue"] for t in issue_types)
    
    def test_auto_correction_capabilities(self, typography_engine):
        """Test auto-correction of common typography issues."""
        # Text with correctable issues
        text_with_issues = 'He said "This has straight quotes" and uses -- dashes.'
        
        result = typography_engine.auto_correct_typography(text_with_issues)
        
        assert "corrected_text" in result
        assert "corrections_made" in result
        assert isinstance(result["corrections_made"], list)
        
        # Check that corrections were applied
        corrected = result["corrected_text"]
        assert """ in corrected or """ in corrected  # Smart quotes
        assert "—" in corrected  # Em dash
    
    def test_typography_scoring_algorithm(self, typography_engine, sample_manuscript_content):
        """Test typography scoring algorithm accuracy."""
        # High quality content
        high_quality_content = sample_manuscript_content["chapters"][0]["content"]
        
        high_quality_score = typography_engine.calculate_typography_score(high_quality_content)
        
        # Low quality content with many issues
        low_quality_content = '''This has many issues.Like missing spaces.And  double  spaces.
        
        It uses "straight quotes" and -- wrong dashes.
        
        And bad    spacing   throughout.'''
        
        low_quality_score = typography_engine.calculate_typography_score(low_quality_content)
        
        assert isinstance(high_quality_score, (int, float))
        assert isinstance(low_quality_score, (int, float))
        assert 0 <= high_quality_score <= 100
        assert 0 <= low_quality_score <= 100
        assert high_quality_score > low_quality_score


class TestPrintFormattingValidation:
    """Test print formatting validation for KDP compliance."""
    
    def test_trim_size_validation(self, print_formatting_engine):
        """Test trim size validation for KDP compliance."""
        # Valid trim sizes
        valid_sizes = ["5x8", "5.25x8", "5.5x8.5", "6x9", "6.14x9.21", "6.69x9.61", "7x10", "7.44x9.69", "7.5x9.25", "8x10", "8.25x6", "8.25x8.25", "8.5x8.5", "8.5x11"]
        
        for size in valid_sizes:
            result = print_formatting_engine.validate_trim_size(size)
            assert result["valid"] is True
            assert "width" in result
            assert "height" in result
        
        # Invalid trim sizes
        invalid_sizes = ["4x6", "12x15", "custom"]
        
        for size in invalid_sizes:
            result = print_formatting_engine.validate_trim_size(size)
            assert result["valid"] is False
            assert "error" in result
    
    def test_margin_validation(self, print_formatting_engine, sample_manuscript_format):
        """Test margin validation for print compliance."""
        result = print_formatting_engine.validate_margins(sample_manuscript_format)
        
        assert "valid" in result
        assert "warnings" in result
        assert "recommendations" in result
        
        # Test with invalid margins (too small)
        invalid_format = ManuscriptFormat(
            **sample_manuscript_format.dict(),
            margin_top=0.25,  # Too small
            margin_inside=0.3   # Too small for binding
        )
        
        result = print_formatting_engine.validate_margins(invalid_format)
        assert result["valid"] is False
        assert len(result["warnings"]) > 0
    
    def test_font_validation(self, print_formatting_engine, sample_manuscript_format):
        """Test font validation for print compliance."""
        result = print_formatting_engine.validate_font_settings(sample_manuscript_format)
        
        assert "valid" in result
        assert "recommendations" in result
        
        # Test with invalid font settings
        invalid_format = ManuscriptFormat(
            **sample_manuscript_format.dict(),
            font_family="Comic Sans MS",  # Not professional
            font_size=8,  # Too small
            line_spacing=3.0  # Too large
        )
        
        result = print_formatting_engine.validate_font_settings(invalid_format)
        assert result["valid"] is False
        assert len(result["recommendations"]) > 0
    
    def test_page_layout_validation(self, print_formatting_engine, sample_manuscript_format):
        """Test page layout validation."""
        result = print_formatting_engine.validate_page_layout(sample_manuscript_format)
        
        assert "valid" in result
        assert "layout_score" in result
        assert "issues" in result
        assert isinstance(result["layout_score"], (int, float))
        assert 0 <= result["layout_score"] <= 100
    
    @patch('langflow.services.publishing.print_formatting_engine.ReportLab')
    def test_pdf_generation_quality(self, mock_reportlab, print_formatting_engine, 
                                  sample_manuscript_content, sample_manuscript_format):
        """Test PDF generation quality validation."""
        # Mock ReportLab components
        mock_canvas = Mock()
        mock_reportlab.pdfgen.canvas.Canvas.return_value = mock_canvas
        
        result = print_formatting_engine.generate_pdf(
            sample_manuscript_content,
            sample_manuscript_format
        )
        
        assert "file_path" in result
        assert "page_count" in result
        assert "file_size" in result
        assert "quality_score" in result
        
        # Verify ReportLab was called with correct parameters
        mock_reportlab.pdfgen.canvas.Canvas.assert_called()
    
    def test_spine_width_calculation(self, print_formatting_engine):
        """Test spine width calculation for print books."""
        # Test with different page counts and paper types
        test_cases = [
            {"pages": 100, "paper": "white", "expected_min": 0.2, "expected_max": 0.3},
            {"pages": 200, "paper": "white", "expected_min": 0.4, "expected_max": 0.5},
            {"pages": 300, "paper": "cream", "expected_min": 0.6, "expected_max": 0.8},
            {"pages": 500, "paper": "cream", "expected_min": 1.0, "expected_max": 1.3}
        ]
        
        for case in test_cases:
            result = print_formatting_engine.calculate_spine_width(
                case["pages"], 
                case["paper"]
            )
            
            assert isinstance(result, float)
            assert case["expected_min"] <= result <= case["expected_max"]
    
    def test_bleed_and_safety_margins(self, print_formatting_engine, sample_manuscript_format):
        """Test bleed and safety margin calculations."""
        result = print_formatting_engine.calculate_bleed_margins(sample_manuscript_format)
        
        assert "bleed_top" in result
        assert "bleed_bottom" in result
        assert "bleed_inside" in result
        assert "bleed_outside" in result
        assert "safety_margin" in result
        
        # Bleed should be 0.125 inches for most trim sizes
        assert result["bleed_top"] == 0.125
        assert result["bleed_bottom"] == 0.125
        assert result["bleed_outside"] == 0.125


class TestEbookGenerationQuality:
    """Test ebook generation quality validation."""
    
    def test_epub_structure_validation(self, ebook_generator, sample_manuscript_content):
        """Test EPUB structure validation."""
        with patch('langflow.services.publishing.ebook_generator.zipfile.ZipFile') as mock_zip:
            mock_zip_instance = Mock()
            mock_zip.return_value.__enter__.return_value = mock_zip_instance
            
            result = ebook_generator.generate_epub(
                sample_manuscript_content,
                "test_workspace_123"
            )
            
            assert "file_path" in result
            assert "validation" in result
            assert result["validation"]["valid_structure"] is True
            
            # Verify required EPUB files were created
            written_files = [call[0][0] for call in mock_zip_instance.writestr.call_args_list]
            required_files = ["META-INF/container.xml", "OEBPS/content.opf", "OEBPS/toc.ncx"]
            
            for required_file in required_files:
                assert any(required_file in written_file for written_file in written_files)
    
    def test_epub_metadata_validation(self, ebook_generator, sample_manuscript_content):
        """Test EPUB metadata validation."""
        metadata = {
            "title": "Test Novel",
            "author": "Test Author",
            "language": "en",
            "isbn": "978-0-123456-78-9",
            "publisher": "Test Publishing"
        }
        
        result = ebook_generator.validate_epub_metadata(metadata)
        
        assert result["valid"] is True
        assert "required_fields_present" in result
        assert result["required_fields_present"] is True
        
        # Test with missing required fields
        incomplete_metadata = {"title": "Test Novel"}
        
        result = ebook_generator.validate_epub_metadata(incomplete_metadata)
        assert result["valid"] is False
        assert "missing_fields" in result
        assert len(result["missing_fields"]) > 0
    
    def test_html_content_validation(self, ebook_generator):
        """Test HTML content validation for ebook."""
        # Valid HTML content
        valid_html = """<html><body><h1>Chapter 1</h1><p>This is a paragraph.</p></body></html>"""
        
        result = ebook_generator.validate_html_content(valid_html)
        assert result["valid"] is True
        assert "errors" in result
        assert len(result["errors"]) == 0
        
        # Invalid HTML content
        invalid_html = """<html><body><h1>Chapter 1<p>Missing closing tag</body></html>"""
        
        result = ebook_generator.validate_html_content(invalid_html)
        assert result["valid"] is False
        assert len(result["errors"]) > 0
    
    def test_css_validation(self, ebook_generator):
        """Test CSS validation for ebook styling."""
        # Valid CSS
        valid_css = """
        body { font-family: serif; font-size: 1em; }
        h1 { font-size: 1.5em; margin-bottom: 1em; }
        p { text-indent: 1em; margin: 0; }
        """
        
        result = ebook_generator.validate_css_styles(valid_css)
        assert result["valid"] is True
        assert "warnings" in result
        
        # CSS with problematic styles
        problematic_css = """
        body { font-size: 8px; } /* Too small */
        h1 { color: #fff; background: #000; } /* High contrast issues */
        """
        
        result = ebook_generator.validate_css_styles(problematic_css)
        assert len(result["warnings"]) > 0
    
    def test_mobi_generation_validation(self, ebook_generator, sample_manuscript_content):
        """Test MOBI generation validation."""
        with patch('langflow.services.publishing.ebook_generator.subprocess.run') as mock_subprocess:
            mock_subprocess.return_value.returncode = 0
            mock_subprocess.return_value.stdout = b"Conversion successful"
            
            result = ebook_generator.generate_mobi(
                sample_manuscript_content,
                "test_workspace_123"
            )
            
            assert "file_path" in result
            assert "conversion_success" in result
            assert result["conversion_success"] is True
            
            # Verify kindlegen was called
            mock_subprocess.assert_called()
    
    def test_ebook_accessibility_validation(self, ebook_generator, sample_manuscript_content):
        """Test ebook accessibility validation."""
        result = ebook_generator.validate_accessibility(sample_manuscript_content)
        
        assert "accessibility_score" in result
        assert "issues" in result
        assert "recommendations" in result
        assert isinstance(result["accessibility_score"], (int, float))
        assert 0 <= result["accessibility_score"] <= 100
        
        # Check for specific accessibility features
        assert "alt_text_coverage" in result
        assert "heading_structure" in result
        assert "reading_order" in result


class TestManuscriptFormattingPerformance:
    """Test manuscript formatting performance with large content."""
    
    @pytest.mark.performance
    def test_large_manuscript_formatting_performance(self, manuscript_formatting_service):
        """Test performance with large manuscripts."""
        # Create large manuscript content
        large_content = {
            "title_page": {"title": "Large Test Novel", "author": "Test Author"},
            "copyright_page": {"copyright_year": 2024, "copyright_holder": "Test Author"},
            "table_of_contents": [],
            "chapters": []
        }
        
        # Generate 50 chapters with substantial content
        for i in range(50):
            chapter_content = f"This is chapter {i+1}. " + ("This is a long sentence with substantial content. " * 200)
            large_content["chapters"].append({
                "title": f"Chapter {i+1}",
                "number": i+1,
                "content": chapter_content
            })
            large_content["table_of_contents"].append({
                "title": f"Chapter {i+1}",
                "page": i*6 + 1
            })
        
        format_spec = ManuscriptFormat(
            publishing_project_id="test_project_123",
            format_type="print",
            trim_size="6x9",
            workspace_id="test_workspace_789"
        )
        
        with patch.object(manuscript_formatting_service, 'generate_pdf') as mock_generate_pdf:
            mock_generate_pdf.return_value = "/tmp/large_manuscript.pdf"
            
            import time
            start_time = time.time()
            
            result = manuscript_formatting_service.apply_print_formatting(
                large_content,
                format_spec
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Should complete within reasonable time
            assert processing_time < 10.0  # Less than 10 seconds for test
            assert result is not None
    
    @pytest.mark.performance
    def test_concurrent_formatting_operations(self, manuscript_formatting_service):
        """Test concurrent formatting operations."""
        format_spec = ManuscriptFormat(
            publishing_project_id="test_project_123",
            format_type="print",
            trim_size="6x9",
            workspace_id="test_workspace_789"
        )
        
        # Mock content for concurrent operations
        mock_content = {
            "title_page": {"title": "Concurrent Test", "author": "Test Author"},
            "chapters": [{"title": "Chapter 1", "number": 1, "content": "Test content. " * 100}]
        }
        
        with patch.object(manuscript_formatting_service, 'apply_print_formatting') as mock_format:
            mock_format.return_value = {"formatted_content": "mock"}
            
            # Create multiple concurrent formatting tasks
            tasks = []
            for i in range(10):
                task = manuscript_formatting_service.apply_print_formatting(mock_content, format_spec)
                tasks.append(task)
            
            import time
            start_time = time.time()
            
            # Execute concurrently (mock operations should be fast)
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Should complete quickly with mocked operations
            assert processing_time < 2.0
            assert len(results) == 10
            assert all(r is not None for r in results if not isinstance(r, Exception))
    
    def test_memory_efficiency_large_content(self, manuscript_formatting_service):
        """Test memory efficiency with large content."""
        # Create content that would use significant memory
        large_chapters = []
        for i in range(100):
            # Each chapter ~100KB of text
            content = "This is substantial chapter content. " * 2500
            large_chapters.append({
                "title": f"Chapter {i+1}",
                "number": i+1,
                "content": content
            })
        
        large_content = {
            "title_page": {"title": "Memory Test Novel", "author": "Test Author"},
            "chapters": large_chapters
        }
        
        # Test memory usage during processing
        import sys
        initial_size = sys.getsizeof(large_content)
        
        typography_engine = TypographyEngine()
        
        # Process content and measure memory impact
        results = []
        for chapter in large_content["chapters"]:
            result = typography_engine.calculate_typography_score(chapter["content"])
            results.append(result)
        
        # Memory usage should be reasonable
        results_size = sys.getsizeof(results)
        assert results_size < initial_size * 0.1  # Results should be much smaller than input


class TestDatabaseCompatibilityFormatting:
    """Test formatting service compatibility with different database providers."""
    
    @pytest.mark.parametrize("database_provider", ["firebase", "sqlite"])
    async def test_manuscript_storage_compatibility(self, database_provider):
        """Test manuscript storage with different database providers."""
        # Mock database operations based on provider
        if database_provider == "firebase":
            mock_db_service = AsyncMock()
            mock_db_service.create_document.return_value = {"id": "test_format_123"}
            mock_db_service.get_document.return_value = {
                "id": "test_format_123",
                "formatted_file_path": "/tmp/manuscript.pdf",
                "file_size": 1048576
            }
        else:
            mock_db_service = AsyncMock()
            mock_db_service.add.return_value = None
            mock_db_service.commit.return_value = None
            mock_db_service.get.return_value = Mock(
                id="test_format_123",
                formatted_file_path="/tmp/manuscript.pdf",
                file_size=1048576
            )
        
        formatting_service = ManuscriptFormattingService()
        
        # Test that formatting service can work with both providers
        format_spec = ManuscriptFormat(
            publishing_project_id="test_project_123",
            format_type="print",
            workspace_id="test_workspace_789"
        )
        
        with patch.object(formatting_service, 'database_service', mock_db_service):
            # The service should adapt to the database provider
            assert formatting_service.database_service is not None
            
            # Mock the actual formatting operation
            with patch.object(formatting_service, 'generate_pdf') as mock_generate:
                mock_generate.return_value = "/tmp/test_manuscript.pdf"
                
                # This should work regardless of database provider
                result = await formatting_service.save_formatted_manuscript(
                    format_spec,
                    "/tmp/test_manuscript.pdf"
                )
                
                assert result["success"] is True


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])