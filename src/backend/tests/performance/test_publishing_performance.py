"""
Performance tests for Amazon KDP Publishing Integration.

This test suite covers:
- Large manuscript processing performance
- Concurrent operation handling
- Memory efficiency optimization
- Database operation performance
- API call optimization
- Publishing pipeline throughput
- Database provider compatibility (Firebase primary, SQL fallback)
"""

import pytest
import asyncio
import time
import psutil
import os
from unittest.mock import AsyncMock, Mock, patch
from datetime import datetime, timedelta
import tempfile
import json
from pathlib import Path
import sys
from concurrent.futures import ThreadPoolExecutor
import threading

from langflow.services.publishing.publishing_workflow_service import PublishingWorkflowService
from langflow.services.publishing.manuscript_formatting_service import ManuscriptFormattingService
from langflow.services.publishing.kdp_service import AmazonKDPService
from langflow.services.publishing.metadata_management_service import MetadataManagementService
from langflow.services.publishing.pricing_calculator import PricingCalculator
from langflow.schema.publishing import ManuscriptFormat, BookMetadata, PublishingProject


@pytest.fixture
def performance_monitor():
    """Create performance monitoring utility."""
    class PerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.start_memory = None
            self.process = psutil.Process()
        
        def start_monitoring(self):
            self.start_time = time.time()
            self.start_memory = self.process.memory_info().rss
        
        def get_metrics(self):
            end_time = time.time()
            end_memory = self.process.memory_info().rss
            
            return {
                "duration": end_time - self.start_time,
                "memory_used": end_memory - self.start_memory,
                "peak_memory": self.process.memory_info().rss,
                "cpu_percent": self.process.cpu_percent()
            }
    
    return PerformanceMonitor()


@pytest.fixture
def large_manuscript_data():
    """Generate large manuscript data for performance testing."""
    def generate_chapter_content(chapter_num, words_per_chapter=5000):
        # Generate realistic content with varied sentence structures
        sentences = [
            "This is the beginning of chapter {}.".format(chapter_num),
            "The story continues with complex narrative structures and detailed character development.",
            "Each paragraph contains substantial content to simulate real manuscript conditions.",
            "Multiple dialogue sections with proper formatting and punctuation are included.",
            "\"This is sample dialogue,\" said the character with proper formatting.",
            "The chapter includes various literary elements including descriptions, action, and introspection.",
            "Technical writing elements such as proper spacing and typography are maintained throughout.",
            "Long paragraphs with multiple sentences help simulate realistic manuscript density and complexity."
        ]
        
        content_parts = []
        words_generated = 0
        
        while words_generated < words_per_chapter:
            for sentence in sentences:
                content_parts.append(sentence)
                words_generated += len(sentence.split())
                if words_generated >= words_per_chapter:
                    break
        
        return " ".join(content_parts)
    
    def create_large_manuscript(num_chapters=100, words_per_chapter=5000):
        chapters = []
        table_of_contents = []
        
        for i in range(num_chapters):
            chapter_num = i + 1
            content = generate_chapter_content(chapter_num, words_per_chapter)
            
            chapters.append({
                "id": f"chapter_{chapter_num}",
                "title": f"Chapter {chapter_num}: The Adventure Continues",
                "number": chapter_num,
                "content": content,
                "word_count": len(content.split())
            })
            
            table_of_contents.append({
                "title": f"Chapter {chapter_num}",
                "page": chapter_num * 6 + 1  # Estimate 6 pages per chapter
            })
        
        return {
            "title_page": {
                "title": "Large Performance Test Novel",
                "subtitle": "A Comprehensive Testing Manuscript",
                "author": "Performance Test Author",
                "publisher": "Test Publishing"
            },
            "copyright_page": {
                "copyright_year": 2024,
                "copyright_holder": "Performance Test Author",
                "isbn": "978-0-123456-78-9"
            },
            "table_of_contents": table_of_contents,
            "chapters": chapters,
            "total_word_count": sum(chapter["word_count"] for chapter in chapters),
            "estimated_page_count": len(chapters) * 6
        }
    
    return create_large_manuscript


@pytest.fixture
def mock_database_service():
    """Mock database service optimized for performance testing."""
    service = AsyncMock()
    
    # Simulate realistic database operation delays
    async def mock_create_with_delay(*args, **kwargs):
        await asyncio.sleep(0.01)  # 10ms delay
        return {"id": f"doc_{int(time.time() * 1000)}"}
    
    async def mock_get_with_delay(*args, **kwargs):
        await asyncio.sleep(0.005)  # 5ms delay
        return {"id": "test_doc", "status": "active"}
    
    async def mock_update_with_delay(*args, **kwargs):
        await asyncio.sleep(0.008)  # 8ms delay
        return {"success": True}
    
    service.create_document = mock_create_with_delay
    service.get_document = mock_get_with_delay
    service.update_document = mock_update_with_delay
    service.query_documents = AsyncMock(return_value=[])
    
    return service


class TestLargeManuscriptPerformance:
    """Test performance with large manuscripts."""
    
    @pytest.mark.performance
    async def test_large_manuscript_formatting_performance(
        self, large_manuscript_data, performance_monitor, mock_database_service
    ):
        """Test manuscript formatting performance with large content."""
        # Generate large manuscript (500k words, ~100 chapters)
        large_manuscript = large_manuscript_data(num_chapters=100, words_per_chapter=5000)
        
        formatting_service = ManuscriptFormattingService()
        
        format_spec = ManuscriptFormat(
            publishing_project_id="test_project_123",
            format_type="print",
            trim_size="6x9",
            workspace_id="test_workspace_789"
        )
        
        with patch.object(formatting_service, 'database_service', mock_database_service), \
             patch.object(formatting_service, 'generate_pdf') as mock_generate_pdf:
            
            mock_generate_pdf.return_value = "/tmp/large_manuscript.pdf"
            
            performance_monitor.start_monitoring()
            
            # Test formatting performance
            result = await formatting_service.apply_print_formatting(
                large_manuscript,
                format_spec
            )
            
            metrics = performance_monitor.get_metrics()
            
            # Performance assertions
            assert metrics["duration"] < 30.0  # Should complete in under 30 seconds
            assert metrics["memory_used"] < 500 * 1024 * 1024  # Less than 500MB additional memory
            assert result is not None
            
            print(f"Large manuscript formatting - Duration: {metrics['duration']:.2f}s, "
                  f"Memory: {metrics['memory_used'] / 1024 / 1024:.2f}MB")
    
    @pytest.mark.performance
    async def test_concurrent_formatting_operations(
        self, large_manuscript_data, performance_monitor, mock_database_service
    ):
        """Test concurrent manuscript formatting operations."""
        formatting_service = ManuscriptFormattingService()
        
        # Create medium-sized manuscripts for concurrent processing
        manuscripts = []
        for i in range(10):
            manuscript = large_manuscript_data(num_chapters=20, words_per_chapter=2000)
            manuscripts.append(manuscript)
        
        format_spec = ManuscriptFormat(
            publishing_project_id="test_project_123",
            format_type="print",
            trim_size="6x9",
            workspace_id="test_workspace_789"
        )
        
        with patch.object(formatting_service, 'database_service', mock_database_service), \
             patch.object(formatting_service, 'generate_pdf') as mock_generate_pdf:
            
            mock_generate_pdf.return_value = "/tmp/manuscript.pdf"
            
            performance_monitor.start_monitoring()
            
            # Create concurrent formatting tasks
            tasks = []
            for i, manuscript in enumerate(manuscripts):
                task = formatting_service.apply_print_formatting(manuscript, format_spec)
                tasks.append(task)
            
            # Execute concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            metrics = performance_monitor.get_metrics()
            
            # Performance assertions
            assert metrics["duration"] < 15.0  # Should be faster than sequential processing
            assert len(results) == 10
            assert all(r is not None for r in results if not isinstance(r, Exception))
            
            print(f"Concurrent formatting - Duration: {metrics['duration']:.2f}s, "
                  f"Operations: 10, Throughput: {10/metrics['duration']:.2f} ops/sec")
    
    @pytest.mark.performance
    async def test_memory_efficiency_large_content(
        self, large_manuscript_data, performance_monitor
    ):
        """Test memory efficiency with very large content."""
        # Generate very large manuscript (1M words)
        very_large_manuscript = large_manuscript_data(num_chapters=200, words_per_chapter=5000)
        
        formatting_service = ManuscriptFormattingService()
        
        performance_monitor.start_monitoring()
        
        # Test memory-efficient processing
        with patch.object(formatting_service, 'process_chapters_in_batches') as mock_batch_process:
            # Simulate batch processing
            def batch_processor(chapters, batch_size=10):
                results = []
                for i in range(0, len(chapters), batch_size):
                    batch = chapters[i:i + batch_size]
                    # Process batch and clear memory
                    batch_result = {"processed": len(batch)}
                    results.append(batch_result)
                return results
            
            mock_batch_process.side_effect = lambda chapters: batch_processor(chapters)
            
            result = formatting_service.process_chapters_in_batches(
                very_large_manuscript["chapters"]
            )
            
            metrics = performance_monitor.get_metrics()
            
            # Memory efficiency assertions
            assert metrics["memory_used"] < 1024 * 1024 * 1024  # Less than 1GB
            assert result is not None
            
            print(f"Large content processing - Memory used: {metrics['memory_used'] / 1024 / 1024:.2f}MB")
    
    @pytest.mark.performance
    async def test_database_operation_performance(
        self, performance_monitor, mock_database_service
    ):
        """Test database operation performance with large datasets."""
        metadata_service = MetadataManagementService()
        
        # Create multiple metadata records
        metadata_records = []
        for i in range(100):
            metadata = {
                "title": f"Performance Test Book {i}",
                "author": f"Author {i}",
                "description": "A comprehensive description for performance testing. " * 10,
                "category": "Fiction > Science Fiction",
                "workspace_id": "test_workspace_789"
            }
            metadata_records.append(metadata)
        
        with patch.object(metadata_service, 'database_service', mock_database_service):
            performance_monitor.start_monitoring()
            
            # Test batch operations
            create_tasks = []
            for metadata in metadata_records:
                task = metadata_service.create_book_metadata(
                    f"project_{len(create_tasks)}",
                    metadata,
                    "test_workspace_789"
                )
                create_tasks.append(task)
            
            # Execute batch operations
            results = await asyncio.gather(*create_tasks, return_exceptions=True)
            
            metrics = performance_monitor.get_metrics()
            
            # Performance assertions
            assert metrics["duration"] < 5.0  # Batch operations should be fast
            assert len(results) == 100
            throughput = 100 / metrics["duration"]
            assert throughput > 20  # At least 20 operations per second
            
            print(f"Database operations - Duration: {metrics['duration']:.2f}s, "
                  f"Throughput: {throughput:.2f} ops/sec")


class TestPublishingWorkflowPerformance:
    """Test publishing workflow performance optimization."""
    
    @pytest.mark.performance
    async def test_end_to_end_workflow_performance(
        self, large_manuscript_data, performance_monitor, mock_database_service
    ):
        """Test complete publishing workflow performance."""
        workflow_service = PublishingWorkflowService()
        
        # Mock all services
        manuscript = large_manuscript_data(num_chapters=50, words_per_chapter=3000)
        
        with patch.object(workflow_service, 'database_service', mock_database_service), \
             patch('langflow.services.publishing.kdp_service.AmazonKDPService') as mock_kdp, \
             patch('langflow.services.publishing.manuscript_formatting_service.ManuscriptFormattingService') as mock_formatting:
            
            # Configure mocks for optimal performance
            mock_kdp_instance = AsyncMock()
            mock_kdp_instance.create_kdp_project.return_value = {"success": True, "kdp_project_id": "kdp_123"}
            mock_kdp_instance.upload_manuscript.return_value = {"success": True, "upload_id": "upload_456"}
            mock_kdp_instance.publish_book.return_value = {"success": True, "status": "published"}
            mock_kdp.return_value = mock_kdp_instance
            
            mock_formatting_instance = AsyncMock()
            mock_formatting_instance.format_manuscript_for_print.return_value = "/tmp/manuscript.pdf"
            mock_formatting.return_value = mock_formatting_instance
            
            performance_monitor.start_monitoring()
            
            # Execute complete workflow
            result = await workflow_service.execute_complete_publishing_workflow(
                "test_book_123",
                ["print", "ebook"],
                "test_workspace_789"
            )
            
            metrics = performance_monitor.get_metrics()
            
            # Performance assertions
            assert metrics["duration"] < 20.0  # Complete workflow under 20 seconds
            assert result["success"] is True
            
            print(f"Complete workflow - Duration: {metrics['duration']:.2f}s")
    
    @pytest.mark.performance
    async def test_parallel_publishing_projects(
        self, performance_monitor, mock_database_service
    ):
        """Test parallel processing of multiple publishing projects."""
        workflow_service = PublishingWorkflowService()
        
        # Create multiple projects for parallel processing
        project_data = []
        for i in range(20):
            project_data.append({
                "book_id": f"book_{i}",
                "formats": ["print"],
                "workspace_id": "test_workspace_789"
            })
        
        with patch.object(workflow_service, 'database_service', mock_database_service):
            performance_monitor.start_monitoring()
            
            # Create parallel initialization tasks
            tasks = []
            for data in project_data:
                task = workflow_service.initialize_publishing_project(
                    data["book_id"],
                    data["formats"],
                    data["workspace_id"]
                )
                tasks.append(task)
            
            # Execute in parallel
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            metrics = performance_monitor.get_metrics()
            
            # Performance assertions
            assert metrics["duration"] < 10.0  # Parallel processing should be efficient
            assert len(results) == 20
            successful_results = [r for r in results if not isinstance(r, Exception) and r.get("success")]
            assert len(successful_results) == 20
            
            throughput = 20 / metrics["duration"]
            print(f"Parallel projects - Duration: {metrics['duration']:.2f}s, "
                  f"Throughput: {throughput:.2f} projects/sec")
    
    @pytest.mark.performance
    async def test_api_call_optimization(self, performance_monitor):
        """Test API call optimization and batching."""
        kdp_service = AmazonKDPService()
        
        # Mock session with realistic delays
        mock_session = AsyncMock()
        
        async def mock_request(*args, **kwargs):
            await asyncio.sleep(0.1)  # 100ms per API call
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.json = AsyncMock(return_value={"success": True})
            return mock_response
        
        mock_session.request.return_value.__aenter__ = AsyncMock(return_value=mock_request())
        mock_session.request.return_value.__aexit__ = AsyncMock(return_value=None)
        kdp_service.session = mock_session
        
        performance_monitor.start_monitoring()
        
        # Test batched API calls
        api_tasks = []
        for i in range(10):
            task = kdp_service.make_kdp_request("GET", f"/test_{i}", {"data": i})
            api_tasks.append(task)
        
        # Execute API calls concurrently
        results = await asyncio.gather(*api_tasks, return_exceptions=True)
        
        metrics = performance_monitor.get_metrics()
        
        # Should be faster than sequential (10 * 0.1 = 1 second)
        assert metrics["duration"] < 0.5  # Should complete much faster with concurrency
        assert len(results) == 10
        
        print(f"API call optimization - Duration: {metrics['duration']:.2f}s for 10 calls")
    
    @pytest.mark.performance
    async def test_caching_performance_impact(self, performance_monitor, mock_database_service):
        """Test performance impact of caching mechanisms."""
        metadata_service = MetadataManagementService()
        
        # Mock cache
        cache = {}
        
        async def cached_get_metadata(metadata_id, workspace_id):
            cache_key = f"{metadata_id}:{workspace_id}"
            if cache_key in cache:
                return cache[cache_key]
            
            # Simulate database fetch
            await asyncio.sleep(0.05)  # 50ms database delay
            result = {"id": metadata_id, "title": f"Cached Book {metadata_id}"}
            cache[cache_key] = result
            return result
        
        with patch.object(metadata_service, 'get_book_metadata', cached_get_metadata):
            # First run - populate cache
            performance_monitor.start_monitoring()
            
            first_run_tasks = []
            for i in range(20):
                task = metadata_service.get_book_metadata(f"meta_{i}", "test_workspace_789")
                first_run_tasks.append(task)
            
            await asyncio.gather(*first_run_tasks)
            first_run_metrics = performance_monitor.get_metrics()
            
            # Second run - use cache
            performance_monitor.start_monitoring()
            
            second_run_tasks = []
            for i in range(20):
                task = metadata_service.get_book_metadata(f"meta_{i}", "test_workspace_789")
                second_run_tasks.append(task)
            
            await asyncio.gather(*second_run_tasks)
            second_run_metrics = performance_monitor.get_metrics()
            
            # Cache should provide significant speedup
            speedup_ratio = first_run_metrics["duration"] / second_run_metrics["duration"]
            assert speedup_ratio > 5  # Should be at least 5x faster with cache
            
            print(f"Caching impact - First run: {first_run_metrics['duration']:.2f}s, "
                  f"Cached run: {second_run_metrics['duration']:.2f}s, "
                  f"Speedup: {speedup_ratio:.1f}x")


class TestDatabasePerformanceOptimization:
    """Test database performance optimization."""
    
    @pytest.mark.performance
    @pytest.mark.parametrize("database_provider", ["firebase", "sqlite"])
    async def test_database_provider_performance(
        self, database_provider, performance_monitor
    ):
        """Test performance differences between database providers."""
        # Mock database services with provider-specific characteristics
        if database_provider == "firebase":
            mock_db_service = AsyncMock()
            # Firebase typically has higher latency but better scalability
            async def firebase_operation(*args, **kwargs):
                await asyncio.sleep(0.02)  # 20ms latency
                return {"success": True}
            
            mock_db_service.create_document = firebase_operation
            mock_db_service.get_document = firebase_operation
            mock_db_service.update_document = firebase_operation
        else:
            mock_db_service = AsyncMock()
            # SQLite typically has lower latency but less scalability
            async def sqlite_operation(*args, **kwargs):
                await asyncio.sleep(0.005)  # 5ms latency
                return Mock(success=True)
            
            mock_db_service.add = sqlite_operation
            mock_db_service.commit = sqlite_operation
            mock_db_service.get = sqlite_operation
        
        workflow_service = PublishingWorkflowService()
        
        with patch.object(workflow_service, 'database_service', mock_db_service):
            performance_monitor.start_monitoring()
            
            # Perform database-intensive operations
            tasks = []
            for i in range(50):
                if database_provider == "firebase":
                    task = mock_db_service.create_document(f"collection", {"id": i})
                else:
                    task = mock_db_service.add(Mock(id=i))
                tasks.append(task)
            
            await asyncio.gather(*tasks)
            
            metrics = performance_monitor.get_metrics()
            
            # Performance characteristics should match provider type
            if database_provider == "firebase":
                # Firebase should handle concurrent operations well
                assert metrics["duration"] < 2.0
            else:
                # SQLite should be faster for individual operations
                assert metrics["duration"] < 1.0
            
            print(f"{database_provider} performance - Duration: {metrics['duration']:.2f}s "
                  f"for 50 operations")
    
    @pytest.mark.performance
    async def test_bulk_operation_optimization(self, performance_monitor, mock_database_service):
        """Test bulk operation optimization."""
        metadata_service = MetadataManagementService()
        
        # Create large dataset for bulk operations
        bulk_data = []
        for i in range(100):
            bulk_data.append({
                "title": f"Bulk Test Book {i}",
                "author": f"Author {i}",
                "category": "Fiction",
                "workspace_id": "test_workspace_789"
            })
        
        with patch.object(metadata_service, 'database_service', mock_database_service):
            # Test individual operations
            performance_monitor.start_monitoring()
            
            individual_tasks = []
            for data in bulk_data[:20]:  # Test with subset
                task = metadata_service.create_book_metadata(
                    f"project_{len(individual_tasks)}",
                    data,
                    "test_workspace_789"
                )
                individual_tasks.append(task)
            
            await asyncio.gather(*individual_tasks)
            individual_metrics = performance_monitor.get_metrics()
            
            # Test bulk operations
            performance_monitor.start_monitoring()
            
            # Mock bulk operation
            async def bulk_create_metadata(metadata_list, workspace_id):
                await asyncio.sleep(0.1)  # Single operation delay
                return {"created_count": len(metadata_list), "success": True}
            
            with patch.object(metadata_service, 'bulk_create_metadata', bulk_create_metadata):
                result = await metadata_service.bulk_create_metadata(bulk_data[:20], "test_workspace_789")
                
            bulk_metrics = performance_monitor.get_metrics()
            
            # Bulk operations should be significantly faster
            efficiency_ratio = individual_metrics["duration"] / bulk_metrics["duration"]
            assert efficiency_ratio > 3  # Should be at least 3x more efficient
            
            print(f"Bulk optimization - Individual: {individual_metrics['duration']:.2f}s, "
                  f"Bulk: {bulk_metrics['duration']:.2f}s, "
                  f"Efficiency: {efficiency_ratio:.1f}x")


class TestPipelineOptimization:
    """Test publishing pipeline optimization."""
    
    @pytest.mark.performance
    async def test_pipeline_stage_optimization(self, performance_monitor, mock_database_service):
        """Test optimization of publishing pipeline stages."""
        workflow_service = PublishingWorkflowService()
        
        # Mock optimized pipeline stages
        async def optimized_stage(stage_name, data):
            # Simulate different stage complexities
            stage_delays = {
                "validation": 0.1,
                "formatting": 0.3,
                "upload": 0.2,
                "publishing": 0.1
            }
            await asyncio.sleep(stage_delays.get(stage_name, 0.1))
            return {"stage": stage_name, "success": True, "data": data}
        
        with patch.object(workflow_service, 'database_service', mock_database_service):
            performance_monitor.start_monitoring()
            
            # Test sequential pipeline
            stages = ["validation", "formatting", "upload", "publishing"]
            project_data = {"project_id": "test_123"}
            
            sequential_results = []
            for stage in stages:
                result = await optimized_stage(stage, project_data)
                sequential_results.append(result)
                project_data.update(result)
            
            sequential_metrics = performance_monitor.get_metrics()
            
            # Test optimized parallel pipeline where possible
            performance_monitor.start_monitoring()
            
            # Some stages can run in parallel
            validation_task = optimized_stage("validation", project_data)
            formatting_task = optimized_stage("formatting", project_data)
            
            # Run validation and formatting in parallel
            parallel_results = await asyncio.gather(validation_task, formatting_task)
            
            # Then run dependent stages
            upload_result = await optimized_stage("upload", parallel_results[1])
            publishing_result = await optimized_stage("publishing", upload_result)
            
            parallel_metrics = performance_monitor.get_metrics()
            
            # Parallel pipeline should be faster
            speedup = sequential_metrics["duration"] / parallel_metrics["duration"]
            assert speedup > 1.2  # At least 20% improvement
            
            print(f"Pipeline optimization - Sequential: {sequential_metrics['duration']:.2f}s, "
                  f"Parallel: {parallel_metrics['duration']:.2f}s, "
                  f"Speedup: {speedup:.1f}x")
    
    @pytest.mark.performance
    async def test_resource_pooling_optimization(self, performance_monitor):
        """Test resource pooling optimization."""
        # Test connection pooling impact
        async def simulate_connection_overhead():
            """Simulate connection establishment overhead."""
            await asyncio.sleep(0.05)  # 50ms connection setup
            return "connection_established"
        
        async def simulate_pooled_connection():
            """Simulate using pooled connection."""
            await asyncio.sleep(0.001)  # 1ms to get from pool
            return "pooled_connection"
        
        # Test without pooling
        performance_monitor.start_monitoring()
        
        no_pool_tasks = []
        for i in range(20):
            task = simulate_connection_overhead()
            no_pool_tasks.append(task)
        
        await asyncio.gather(*no_pool_tasks)
        no_pool_metrics = performance_monitor.get_metrics()
        
        # Test with pooling
        performance_monitor.start_monitoring()
        
        # Establish pool once
        pool_setup = await simulate_connection_overhead()
        
        pooled_tasks = []
        for i in range(20):
            task = simulate_pooled_connection()
            pooled_tasks.append(task)
        
        await asyncio.gather(*pooled_tasks)
        pooled_metrics = performance_monitor.get_metrics()
        
        # Pooling should provide significant improvement
        improvement_ratio = no_pool_metrics["duration"] / pooled_metrics["duration"]
        assert improvement_ratio > 5  # Should be much faster with pooling
        
        print(f"Resource pooling - No pool: {no_pool_metrics['duration']:.2f}s, "
              f"With pool: {pooled_metrics['duration']:.2f}s, "
              f"Improvement: {improvement_ratio:.1f}x")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "-m", "performance"])